{"data_mtime": 1754379926, "dep_lines": [1, 2, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30], "dependencies": ["rich.default_styles", "rich.theme", "builtins", "_frozen_importlib", "abc", "rich.style", "typing"], "hash": "5962944738f3a08c35e5119f576c85edff8c58c0", "id": "rich.themes", "ignore_all": true, "interface_hash": "e867d0fc1c465660d09520535f33d78d62f88eb3", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\themes.py", "plugin_data": null, "size": 102, "suppressed": [], "version_id": "1.15.0"}