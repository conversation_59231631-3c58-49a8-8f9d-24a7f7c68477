{".class": "MypyFile", "_fullname": "markdown_it.rules_block.state_block", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EnvType": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.EnvType", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MarkdownIt": {".class": "SymbolTableNode", "cross_ref": "markdown_it.main.MarkdownIt", "kind": "Gdef"}, "StateBase": {".class": "SymbolTableNode", "cross_ref": "markdown_it.ruler.StateBase", "kind": "Gdef"}, "StateBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown_it.ruler.StateBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown_it.rules_block.state_block.StateBlock", "name": "StateBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown_it.rules_block.state_block", "mro": ["markdown_it.rules_block.state_block.StateBlock", "markdown_it.ruler.StateBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "src", "md", "env", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "src", "md", "env", "tokens"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.str", "markdown_it.main.MarkdownIt", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}, {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StateBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["markdown_it.rules_block.state_block.StateBlock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of StateBlock", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_code_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock._code_enabled", "name": "_code_enabled", "type": "builtins.bool"}}, "bMarks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.bMarks", "name": "bMarks", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "blkIndent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.blkIndent", "name": "blkIndent", "type": "builtins.int"}}, "bsCount": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.bsCount", "name": "bsCount", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ddIndent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.ddIndent", "name": "ddIndent", "type": "builtins.int"}}, "eMarks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.eMarks", "name": "eMarks", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "getLines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "begin", "end", "indent", "keepLastLF"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.getLines", "name": "getLines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "begin", "end", "indent", "keepLastLF"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getLines of StateBlock", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isEmpty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.isEmpty", "name": "isEmpty", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isEmpty of StateBlock", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_code_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.is_code_block", "name": "is_code_block", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_code_block of StateBlock", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.level", "name": "level", "type": "builtins.int"}}, "line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.line", "name": "line", "type": "builtins.int"}}, "lineMax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.lineMax", "name": "lineMax", "type": "builtins.int"}}, "listIndent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.listIndent", "name": "listIndent", "type": "builtins.int"}}, "parentType": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.parentType", "name": "parentType", "type": "builtins.str"}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ttype", "tag", "nesting"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ttype", "tag", "nesting"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of StateBlock", "ret_type": "markdown_it.token.Token", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.result", "name": "result", "type": "builtins.str"}}, "sCount": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.sCount", "name": "sCount", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "skipChars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pos", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.skipChars", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pos", "code"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON> of StateBlock", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skipCharsBack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pos", "code", "minimum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.skipCharsBack", "name": "skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pos", "code", "minimum"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip<PERSON><PERSON><PERSON><PERSON>ack of StateBlock", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skipCharsStr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pos", "ch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.skipCharsStr", "name": "skipCharsStr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pos", "ch"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skipCharsStr of StateBlock", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skipCharsStrBack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pos", "ch", "minimum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.skipCharsStrBack", "name": "skipCharsStrBack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pos", "ch", "minimum"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skipCharsStrBack of StateBlock", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skipEmptyLines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "from_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.skipEmptyLines", "name": "skipEmptyLines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "from_pos"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skipEmptyLines of StateBlock", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skipSpaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.skipSpaces", "name": "skipSpaces", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pos"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skipSpaces of StateBlock", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skipSpacesBack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pos", "minimum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.state_block.StateBlock.skipSpacesBack", "name": "skipSpacesBack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pos", "minimum"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skipSpacesBack of StateBlock", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tShift": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.tShift", "name": "tShift", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "tight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.tight", "name": "tight", "type": "builtins.bool"}}, "tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.rules_block.state_block.StateBlock.tokens", "name": "tokens", "type": {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown_it.rules_block.state_block.StateBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown_it.rules_block.state_block.StateBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "markdown_it.token.Token", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.state_block.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.state_block.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.state_block.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.state_block.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.state_block.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.state_block.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "isStrSpace": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.isStrSpace", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_block\\state_block.py"}