{".class": "MypyFile", "_fullname": "rich.text", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlignMethod": {".class": "SymbolTableNode", "cross_ref": "rich.align.AlignMethod", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Console": {".class": "SymbolTableNode", "cross_ref": "rich.console.Console", "kind": "Gdef"}, "ConsoleOptions": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleOptions", "kind": "Gdef"}, "DEFAULT_JUSTIFY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.text.DEFAULT_JUSTIFY", "name": "DEFAULT_JUSTIFY", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}}}, "DEFAULT_OVERFLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.text.DEFAULT_OVERFLOW", "name": "DEFAULT_OVERFLOW", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EmojiVariant": {".class": "SymbolTableNode", "cross_ref": "rich.emoji.EmojiV<PERSON>t", "kind": "Gdef"}, "GetStyleCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.text.GetStyleCallable", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "JupyterMixin": {".class": "SymbolTableNode", "cross_ref": "rich.jupyter.Ju<PERSON>", "kind": "Gdef"}, "JustifyMethod": {".class": "SymbolTableNode", "cross_ref": "rich.console.JustifyMethod", "kind": "Gdef"}, "Lines": {".class": "SymbolTableNode", "cross_ref": "rich.containers.Lines", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Measurement": {".class": "SymbolTableNode", "cross_ref": "rich.measure.Measurement", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OverflowMethod": {".class": "SymbolTableNode", "cross_ref": "rich.console.OverflowMethod", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "Segment": {".class": "SymbolTableNode", "cross_ref": "rich.segment.Segment", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.text.Span", "name": "Span", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "rich.text.Span", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["start", "end", "style"]}}, "module_name": "rich.text", "mro": ["rich.text.Span", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.text.Span.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": "rich.text.Span"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of Span", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.text.Span.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.text.Span.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "start"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "end"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "style"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "start", "end", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "rich.text.Span.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "start", "end", "style"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Span", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": "rich.text.Span"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Span", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of Span", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.text.Span._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.text.Span._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.text.Span._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.text.Span._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Span", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "rich.text.Span._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Span", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "start", "end", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "start", "end", "style"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of Span", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span._NT", "id": -1, "name": "_NT", "namespace": "rich.text.Span._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.text.Span._source", "name": "_source", "type": "builtins.str"}}, "end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.text.Span.end", "name": "end", "type": "builtins.int"}}, "end-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.text.Span.end", "kind": "<PERSON><PERSON><PERSON>"}, "extend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cells"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span.extend", "name": "extend", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cells"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": "rich.text.Span"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extend of Span", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "move": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span.move", "name": "move", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": "rich.text.Span"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of Span", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "right_crop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span.right_crop", "name": "right_crop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": "rich.text.Span"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "right_crop of Span", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Span.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": "rich.text.Span"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Span", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.text.Span.start", "name": "start", "type": "builtins.int"}}, "start-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.text.Span.start", "kind": "<PERSON><PERSON><PERSON>"}, "style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.text.Span.style", "name": "style", "type": {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}}}, "style-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.text.Span.style", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Span.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": "rich.text.Span"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Style": {".class": "SymbolTableNode", "cross_ref": "rich.style.Style", "kind": "Gdef"}, "StyleType": {".class": "SymbolTableNode", "cross_ref": "rich.style.StyleType", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.jupyter.Ju<PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.text.Text", "name": "Text", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.text.Text", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.text", "mro": ["rich.text.Text", "rich.jupyter.Ju<PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["rich.text.Text", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["rich.text.Text", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["rich.text.Text", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "text", "style", "justify", "overflow", "no_wrap", "end", "tab_size", "spans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "text", "style", "justify", "overflow", "no_wrap", "end", "tab_size", "spans"], "arg_types": ["rich.text.Text", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of Text", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.text.Text", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of Text", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_measure__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__rich_measure__", "name": "__rich_measure__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.text.Text", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_measure__ of Text", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.measure.Measurement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "rich.text.Text.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.text.Text._length", "name": "_length", "type": "builtins.int"}}, "_spans": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.text.Text._spans", "name": "_spans", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.text.Text._text", "name": "_text", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_trim_spans": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text._trim_spans", "name": "_trim_spans", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_trim_spans of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "align": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "align", "width", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.align", "name": "align", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "align", "width", "character"], "arg_types": ["rich.text.Text", {".class": "TypeAliasType", "args": [], "type_ref": "rich.align.AlignMethod"}, "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "align of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": ["rich.text.Text", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "rich.style.Style", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.append_text", "name": "append_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["rich.text.Text", "rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_text of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.append_tokens", "name": "append_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "arg_types": ["rich.text.Text", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_tokens of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "meta", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.apply_meta", "name": "apply_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "meta", "start", "end"], "arg_types": ["rich.text.Text", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_meta of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assemble": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "parts", "style", "justify", "overflow", "no_wrap", "end", "tab_size", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.text.Text.assemble", "name": "assemble", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "parts", "style", "justify", "overflow", "no_wrap", "end", "tab_size", "meta"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, {".class": "UnionType", "items": ["builtins.str", "rich.text.Text", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assemble of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.text.Text.assemble", "name": "assemble", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "parts", "style", "justify", "overflow", "no_wrap", "end", "tab_size", "meta"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, {".class": "UnionType", "items": ["builtins.str", "rich.text.Text", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assemble of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "blank_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "plain"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.blank_copy", "name": "blank_copy", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "plain"], "arg_types": ["rich.text.Text", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blank_copy of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cell_len": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.text.Text.cell_len", "name": "cell_len", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cell_len of Text", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.text.Text.cell_len", "name": "cell_len", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cell_len of Text", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.copy_styles", "name": "copy_styles", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["rich.text.Text", "rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_styles of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detect_indentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.detect_indentation", "name": "detect_indentation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_indentation of Text", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "divide": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "offsets"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.divide", "name": "divide", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "offsets"], "arg_types": ["rich.text.Text", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide of Text", "ret_type": "rich.containers.Lines", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.text.Text.end", "name": "end", "type": "builtins.str"}}, "expand_tabs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "tab_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.expand_tabs", "name": "expand_tabs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "tab_size"], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_tabs of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extend_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "spaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.extend_style", "name": "extend_style", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "spaces"], "arg_types": ["rich.text.Text", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extend_style of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["rich.text.Text", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of Text", "ret_type": "rich.containers.Lines", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_ansi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "text", "style", "justify", "overflow", "no_wrap", "end", "tab_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.text.Text.from_ansi", "name": "from_ansi", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "text", "style", "justify", "overflow", "no_wrap", "end", "tab_size"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_ansi of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.text.Text.from_ansi", "name": "from_ansi", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "text", "style", "justify", "overflow", "no_wrap", "end", "tab_size"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_ansi of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "text", "style", "emoji", "emoji_variant", "justify", "overflow", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.text.Text.from_markup", "name": "from_markup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "text", "style", "emoji", "emoji_variant", "justify", "overflow", "end"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.emoji.EmojiV<PERSON>t"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_markup of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.text.Text.from_markup", "name": "from_markup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "text", "style", "emoji", "emoji_variant", "justify", "overflow", "end"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.emoji.EmojiV<PERSON>t"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_markup of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_style_at_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.get_style_at_offset", "name": "get_style_at_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "offset"], "arg_types": ["rich.text.Text", "rich.console.Console", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_style_at_offset of Text", "ret_type": "rich.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "highlight_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "re_highlight", "style", "style_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.highlight_regex", "name": "highlight_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "re_highlight", "style", "style_prefix"], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.GetStyleCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "highlight_regex of Text", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "highlight_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "words", "style", "case_sensitive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.highlight_words", "name": "highlight_words", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "words", "style", "case_sensitive"], "arg_types": ["rich.text.Text", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "highlight_words of Text", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["rich.text.Text", {".class": "Instance", "args": ["rich.text.Text"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "justify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.text.Text.justify", "name": "justify", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.text.Text.markup", "name": "markup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "markup of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.text.Text.markup", "name": "markup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "markup of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "no_wrap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.text.Text.no_wrap", "name": "no_wrap", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "meta", "handlers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "meta", "handlers"], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overflow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.text.Text.overflow", "name": "overflow", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.pad", "name": "pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "character"], "arg_types": ["rich.text.Text", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pad of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad_left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.pad_left", "name": "pad_left", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "character"], "arg_types": ["rich.text.Text", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pad_left of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad_right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.pad_right", "name": "pad_right", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "character"], "arg_types": ["rich.text.Text", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pad_right of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "rich.text.Text.plain", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "rich.text.Text.plain", "name": "plain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plain of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "rich.text.Text.plain", "name": "plain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plain of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "rich.text.Text.plain", "name": "plain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_text"], "arg_types": ["rich.text.Text", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plain of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "plain", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plain of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "remove_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.remove_suffix", "name": "remove_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "suffix"], "arg_types": ["rich.text.Text", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_suffix of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "console", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "console", "end"], "arg_types": ["rich.text.Text", "rich.console.Console", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Text", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "right_crop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.right_crop", "name": "right_crop", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "amount"], "arg_types": ["rich.text.Text", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "right_crop of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rstrip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.rstrip", "name": "rstrip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rstrip_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.rstrip_end", "name": "rstrip_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "size"], "arg_types": ["rich.text.Text", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip_end of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.set_length", "name": "set_length", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_length"], "arg_types": ["rich.text.Text", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_length of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "spans": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "rich.text.Text.spans", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "rich.text.Text.spans", "name": "spans", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spans of Text", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "rich.text.Text.spans", "name": "spans", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spans of Text", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "spans"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "rich.text.Text.spans", "name": "spans", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "spans"], "arg_types": ["rich.text.Text", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spans of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "spans", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spans of Text", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.text.Span"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "split": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5], "arg_names": ["self", "separator", "include_separator", "allow_blank"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": ["self", "separator", "include_separator", "allow_blank"], "arg_types": ["rich.text.Text", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Text", "ret_type": "rich.containers.Lines", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.text.Text.style", "name": "style", "type": {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}}}, "styled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["cls", "text", "style", "justify", "overflow"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.text.Text.styled", "name": "styled", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["cls", "text", "style", "justify", "overflow"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "styled of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.text.Text.styled", "name": "styled", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["cls", "text", "style", "justify", "overflow"], "arg_types": [{".class": "TypeType", "item": "rich.text.Text"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "styled of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stylize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "style", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.stylize", "name": "stylize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "style", "start", "end"], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stylize of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stylize_before": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "style", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.stylize_before", "name": "stylize_before", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "style", "start", "end"], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stylize_before of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.text.Text.tab_size", "name": "tab_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "truncate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "max_width", "overflow", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.truncate", "name": "truncate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "max_width", "overflow", "pad"], "arg_types": ["rich.text.Text", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "truncate of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_indent_guides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5], "arg_names": ["self", "indent_size", "character", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.with_indent_guides", "name": "with_indent_guides", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": ["self", "indent_size", "character", "style"], "arg_types": ["rich.text.Text", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_indent_guides of Text", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["self", "console", "width", "justify", "overflow", "tab_size", "no_wrap"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.text.Text.wrap", "name": "wrap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["self", "console", "width", "justify", "overflow", "tab_size", "no_wrap"], "arg_types": ["rich.text.Text", "rich.console.Console", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap of Text", "ret_type": "rich.containers.Lines", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.text.Text.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.text.Text", "values": [], "variance": 0}, "slots": ["_length", "_spans", "_text", "end", "justify", "no_wrap", "overflow", "style", "tab_size"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.text.TextType", "line": 41, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "rich.text.Text"], "uses_pep604_syntax": false}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.text.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.text.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.text.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.text.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.text.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.text.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_re_whitespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.text._re_whitespace", "name": "_re_whitespace", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "cell_len": {".class": "SymbolTableNode", "cross_ref": "rich.cells.cell_len", "kind": "Gdef"}, "console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.text.console", "name": "console", "type": "rich.console.Console"}}, "divide_line": {".class": "SymbolTableNode", "cross_ref": "rich._wrap.divide_line", "kind": "Gdef"}, "gcd": {".class": "SymbolTableNode", "cross_ref": "math.gcd", "kind": "Gdef"}, "itemgetter": {".class": "SymbolTableNode", "cross_ref": "operator.itemgetter", "kind": "Gdef"}, "loop_last": {".class": "SymbolTableNode", "cross_ref": "rich._loop.loop_last", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "pick_bool": {".class": "SymbolTableNode", "cross_ref": "rich._pick.pick_bool", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reduce": {".class": "SymbolTableNode", "cross_ref": "functools.reduce", "kind": "Gdef"}, "set_cell_size": {".class": "SymbolTableNode", "cross_ref": "rich.cells.set_cell_size", "kind": "Gdef"}, "strip_control_codes": {".class": "SymbolTableNode", "cross_ref": "rich.control.strip_control_codes", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.text.text", "name": "text", "type": "rich.text.Text"}}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\text.py"}