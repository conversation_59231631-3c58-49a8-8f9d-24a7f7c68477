{"data_mtime": 1754379926, "dep_lines": [30, 40, 41, 42, 43, 44, 45, 46, 47, 50, 1, 2, 3, 4, 5, 6, 7, 8, 12, 13, 14, 33, 39, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 10, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.repr", "rich._loop", "rich._pick", "rich.abc", "rich.cells", "rich.highlighter", "rich.jupyter", "rich.measure", "rich.text", "rich.console", "builtins", "collections", "dataclasses", "inspect", "os", "reprlib", "sys", "array", "itertools", "types", "typing", "attr", "rich", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "rich.segment", "rich.style", "typing_extensions"], "hash": "9504bdf728b8cd87af4285ad8dce7997a3a2c12e", "id": "rich.pretty", "ignore_all": true, "interface_hash": "ca5215469d9e1054667b92e7ffca7d7888656e20", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\pretty.py", "plugin_data": null, "size": 36355, "suppressed": [], "version_id": "1.15.0"}