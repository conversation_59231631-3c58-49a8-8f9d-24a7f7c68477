{".class": "MypyFile", "_fullname": "markdown_it.common.html_re", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HTML_OPEN_CLOSE_TAG_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.HTML_OPEN_CLOSE_TAG_RE", "name": "HTML_OPEN_CLOSE_TAG_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "HTML_OPEN_CLOSE_TAG_STR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.HTML_OPEN_CLOSE_TAG_STR", "name": "HTML_OPEN_CLOSE_TAG_STR", "type": "builtins.str"}}, "HTML_TAG_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.HTML_TAG_RE", "name": "HTML_TAG_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.common.html_re.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.common.html_re.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.common.html_re.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.common.html_re.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.common.html_re.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.common.html_re.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "attr_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.attr_name", "name": "attr_name", "type": "builtins.str"}}, "attr_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.attr_value", "name": "attr_value", "type": "builtins.str"}}, "attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.attribute", "name": "attribute", "type": "builtins.str"}}, "cdata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.cdata", "name": "cdata", "type": "builtins.str"}}, "close_tag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.close_tag", "name": "close_tag", "type": "builtins.str"}}, "comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.comment", "name": "comment", "type": "builtins.str"}}, "declaration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.declaration", "name": "declaration", "type": "builtins.str"}}, "double_quoted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.double_quoted", "name": "double_quoted", "type": "builtins.str"}}, "open_tag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.open_tag", "name": "open_tag", "type": "builtins.str"}}, "processing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.processing", "name": "processing", "type": "builtins.str"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "single_quoted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.single_quoted", "name": "single_quoted", "type": "builtins.str"}}, "unquoted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.common.html_re.unquoted", "name": "unquoted", "type": "builtins.str"}}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\common\\html_re.py"}