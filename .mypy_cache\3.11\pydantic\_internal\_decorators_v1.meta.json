{"data_mtime": 1749951571, "dep_lines": [12, 8, 11, 3, 5, 6, 8, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 20, 5, 5, 30, 30, 30, 30], "dependencies": ["pydantic._internal._utils", "pydantic_core.core_schema", "pydantic.errors", "__future__", "inspect", "typing", "pydantic_core", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "80556ed82bba095445f28d33c610082fa56a950c", "id": "pydantic._internal._decorators_v1", "ignore_all": true, "interface_hash": "8d40d0aee6de54fa1a46f935fcc1f4a8a89dc573", "mtime": 1749916492, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py", "plugin_data": null, "size": 6185, "suppressed": [], "version_id": "1.15.0"}