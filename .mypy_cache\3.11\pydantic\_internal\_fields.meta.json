{"data_mtime": 1749951571, "dep_lines": [22, 22, 23, 24, 25, 26, 27, 28, 35, 36, 7, 16, 17, 20, 22, 33, 34, 3, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 19, 31, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 5, 10, 5, 5, 20, 25, 25, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["pydantic._internal._generics", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic._internal._docs_extraction", "pydantic._internal._import_utils", "pydantic._internal._namespace_utils", "pydantic._internal._repr", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic._internal._decorators", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic.errors", "pydantic._internal", "pydantic.fields", "pydantic.main", "__future__", "dataclasses", "warnings", "copy", "functools", "inspect", "re", "typing", "pydantic_core", "typing_extensions", "typing_inspection", "pydantic", "annotated_types", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal._model_construction"], "hash": "6c7e501372e76346e2dab68e52c9c4fc50acf64f", "id": "pydantic._internal._fields", "ignore_all": true, "interface_hash": "4b370c9befa2ef92c80e9f2a503638d0a2bdbd64", "mtime": 1749916492, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_fields.py", "plugin_data": null, "size": 23205, "suppressed": [], "version_id": "1.15.0"}