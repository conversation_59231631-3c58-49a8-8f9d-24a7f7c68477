"""
简化版Rich日志美化系统
解决中文字符对齐和终端抖动问题
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from collections import deque

from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.live import Live
from rich.align import Align
from rich.box import ROUNDED, SIMPLE
from rich.logging import RichHandler

class ComponentType(Enum):
    """系统组件类型"""
    API = "API服务器"
    RADAR_SERVER = "雷达服务器"
    RADAR_CLIENT = "雷达客户端"
    RADAR_SIMULATOR = "雷达模拟器"
    DATABASE = "数据库"
    SYSTEM = "系统"

class SimpleRichLogger:
    """简化版Rich日志系统"""
    
    def __init__(self):
        self.console = Console(width=120, legacy_windows=False)  # 固定宽度避免抖动
        self.logs = {component: deque(maxlen=8) for component in ComponentType}  # 减少显示行数
        
        # 系统状态
        self.component_status = {
            ComponentType.API: "🔴 未启动",
            ComponentType.RADAR_SERVER: "🔴 未启动", 
            ComponentType.RADAR_CLIENT: "🔴 未连接",
            ComponentType.DATABASE: "🔴 未连接",
            ComponentType.SYSTEM: "🟡 初始化中"
        }
        
        # 雷达连接（简化）
        self.radar_count = 0
        self.radar_online = 0
        
        # 统计信息
        self.stats = {
            "api_requests": 0,
            "radar_commands": 0,
            "errors": 0,
            "start_time": datetime.now()
        }
        
        self.layout = self._create_layout()
        self.live = None
        self.running = False
        self.update_lock = threading.Lock()  # 防止并发更新
        
    def _create_layout(self) -> Layout:
        """创建简化布局"""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="logs", ratio=2),
            Layout(name="status", ratio=1)
        )
        
        return layout
    
    def _create_header(self) -> Panel:
        """创建头部"""
        uptime = datetime.now() - self.stats["start_time"]
        uptime_str = str(uptime).split('.')[0]
        
        header_text = f"雷达管理系统监控 | 运行时间: {uptime_str}"
        return Panel(
            Align.center(Text(header_text, style="bold cyan")),
            box=SIMPLE,
            style="cyan"
        )
    
    def _create_logs_panel(self) -> Panel:
        """创建日志面板"""
        # 创建两列日志显示
        left_logs = self._format_component_logs([ComponentType.API, ComponentType.DATABASE])
        right_logs = self._format_component_logs([ComponentType.RADAR_SERVER, ComponentType.SYSTEM])
        
        # 使用表格确保对齐
        table = Table.grid(padding=1)
        table.add_column("left", ratio=1)
        table.add_column("right", ratio=1)
        table.add_row(left_logs, right_logs)
        
        return Panel(
            table,
            title="系统日志",
            border_style="white",
            box=ROUNDED
        )
    
    def _format_component_logs(self, components: List[ComponentType]) -> Text:
        """格式化组件日志"""
        text = Text()
        
        for component in components:
            # 组件标题
            icon = self._get_component_icon(component)
            color = self._get_component_color(component)
            text.append(f"{icon} {component.value}\n", style=f"bold {color}")
            
            # 日志条目
            logs = list(self.logs[component])[-6:]  # 最多显示6条
            if not logs:
                text.append("  暂无日志\n", style="dim")
            else:
                for log in logs:
                    time_str = log['time'].strftime("%H:%M:%S")
                    level_icon = self._get_level_icon(log['level'])
                    message = log['message'][:40] + "..." if len(log['message']) > 40 else log['message']
                    
                    text.append(f"  [{time_str}] {level_icon} {message}\n", style=color)
            
            text.append("\n")  # 组件间空行
        
        return text
    
    def _create_status_panel(self) -> Panel:
        """创建状态面板"""
        # 系统状态
        status_text = Text()
        status_text.append("系统状态\n", style="bold yellow")
        
        for component, status in self.component_status.items():
            if component == ComponentType.RADAR_SIMULATOR:
                continue  # 跳过模拟器显示
            status_text.append(f"{component.value}: {status}\n", style="white")
        
        status_text.append(f"\n雷达连接: {self.radar_online}/{self.radar_count}\n", style="green")
        
        # 统计信息
        status_text.append("\n统计信息\n", style="bold yellow")
        status_text.append(f"API请求: {self.stats['api_requests']}\n", style="cyan")
        status_text.append(f"雷达命令: {self.stats['radar_commands']}\n", style="blue")
        status_text.append(f"错误数量: {self.stats['errors']}\n", style="red")
        
        return Panel(
            status_text,
            title="系统状态",
            border_style="yellow",
            box=ROUNDED
        )
    
    def _create_footer(self) -> Panel:
        """创建底部"""
        footer_text = f"当前时间: {datetime.now().strftime('%H:%M:%S')} | 按 Ctrl+C 退出"
        return Panel(
            Align.center(Text(footer_text, style="dim")),
            box=SIMPLE,
            style="dim"
        )
    
    def _get_component_icon(self, component: ComponentType) -> str:
        """获取组件图标"""
        icons = {
            ComponentType.API: "🌐",
            ComponentType.RADAR_SERVER: "📡",
            ComponentType.RADAR_CLIENT: "📱",
            ComponentType.DATABASE: "🗄️",
            ComponentType.SYSTEM: "🖥️"
        }
        return icons.get(component, "📦")
    
    def _get_component_color(self, component: ComponentType) -> str:
        """获取组件颜色"""
        colors = {
            ComponentType.API: "cyan",
            ComponentType.RADAR_SERVER: "green",
            ComponentType.RADAR_CLIENT: "blue",
            ComponentType.DATABASE: "yellow",
            ComponentType.SYSTEM: "white"
        }
        return colors.get(component, "white")
    
    def _get_level_icon(self, level: str) -> str:
        """获取日志级别图标"""
        icons = {
            "DEBUG": "🔍",
            "INFO": "ℹ️",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "CRITICAL": "🚨"
        }
        return icons.get(level.upper(), "📝")
    
    def add_log(self, component: ComponentType, level: str, message: str, radar_id: Optional[str] = None):
        """添加日志"""
        with self.update_lock:
            log_entry = {
                'time': datetime.now(),
                'level': level,
                'message': message,
                'radar_id': radar_id
            }
            
            self.logs[component].append(log_entry)
            
            # 更新统计
            if level.upper() == "ERROR":
                self.stats["errors"] += 1
    
    def update_component_status(self, component: ComponentType, status: str):
        """更新组件状态"""
        with self.update_lock:
            self.component_status[component] = status
    
    def update_radar_stats(self, total: int, online: int):
        """更新雷达统计"""
        with self.update_lock:
            self.radar_count = total
            self.radar_online = online
    
    def increment_stat(self, stat_name: str, amount: int = 1):
        """增加统计"""
        with self.update_lock:
            if stat_name in self.stats:
                self.stats[stat_name] += amount
    
    def _update_display(self):
        """更新显示"""
        while self.running:
            try:
                with self.update_lock:
                    self.layout["header"].update(self._create_header())
                    self.layout["logs"].update(self._create_logs_panel())
                    self.layout["status"].update(self._create_status_panel())
                    self.layout["footer"].update(self._create_footer())
                
                time.sleep(2)  # 降低刷新频率减少抖动
            except Exception:
                pass  # 静默处理错误
    
    def start(self):
        """启动系统"""
        if self.running:
            return
            
        self.running = True
        
        # 启动Live显示
        self.live = Live(
            self.layout,
            console=self.console,
            refresh_per_second=0.5,  # 降低刷新率
            screen=True
        )
        
        # 启动更新线程
        update_thread = threading.Thread(target=self._update_display, daemon=True)
        update_thread.start()
        
        self.live.start()
        self.add_log(ComponentType.SYSTEM, "INFO", "Rich日志系统已启动")
    
    def stop(self):
        """停止系统"""
        if not self.running:
            return
            
        self.running = False
        if self.live:
            self.live.stop()

# 全局实例
simple_rich_logger = SimpleRichLogger()

class SimpleRichHandler(logging.Handler):
    """简化Rich日志处理器"""
    
    def __init__(self, component: ComponentType):
        super().__init__()
        self.component = component
    
    def emit(self, record):
        """发送日志"""
        try:
            message = record.getMessage()
            
            # 提取雷达ID
            radar_id = None
            import re
            radar_match = re.search(r'\[雷达(\w+)\]', message)
            if radar_match:
                radar_id = radar_match.group(1)
            
            simple_rich_logger.add_log(
                component=self.component,
                level=record.levelname,
                message=message,
                radar_id=radar_id
            )
        except Exception:
            pass

def setup_simple_rich_logging():
    """设置简化Rich日志"""
    simple_rich_logger.start()
    
    def get_logger(name: str, component: ComponentType) -> logging.Logger:
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        logger.handlers.clear()
        
        handler = SimpleRichHandler(component)
        handler.setLevel(logging.INFO)
        logger.addHandler(handler)
        
        return logger
    
    return get_logger
