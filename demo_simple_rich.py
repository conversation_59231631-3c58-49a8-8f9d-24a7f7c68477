"""
简化版Rich日志系统演示
"""

import time
import threading
from simple_rich_logger import simple_rich_logger, ComponentType

def demo_simple_rich():
    """演示简化Rich日志系统"""
    print("🎨 启动简化版Rich日志系统演示...")
    print("按 Ctrl+C 退出演示")
    
    # 启动系统
    simple_rich_logger.start()
    
    # 更新组件状态
    simple_rich_logger.update_component_status(ComponentType.API, "🟢 运行中")
    simple_rich_logger.update_component_status(ComponentType.RADAR_SERVER, "🟢 运行中")
    simple_rich_logger.update_component_status(ComponentType.DATABASE, "🟢 已连接")
    
    # 更新雷达统计
    simple_rich_logger.update_radar_stats(3, 2)
    
    # 模拟日志输出
    logs = [
        (ComponentType.API, "INFO", "Flask应用启动成功"),
        (ComponentType.DATABASE, "INFO", "数据库连接成功"),
        (ComponentType.RADAR_SERVER, "INFO", "雷达服务器启动"),
        (ComponentType.API, "INFO", "用户登录成功"),
        (ComponentType.RADAR_SERVER, "INFO", "雷达001连接成功", "001"),
        (ComponentType.RADAR_SERVER, "INFO", "雷达002开始工作", "002"),
        (ComponentType.API, "INFO", "处理API请求: /radar_manage/list"),
        (ComponentType.RADAR_SERVER, "WARNING", "雷达003连接超时", "003"),
        (ComponentType.API, "WARNING", "API请求频率过高"),
        (ComponentType.RADAR_SERVER, "ERROR", "雷达003通信失败", "003"),
        (ComponentType.SYSTEM, "INFO", "系统运行正常"),
        (ComponentType.DATABASE, "INFO", "数据同步完成"),
    ]
    
    try:
        # 逐步添加日志
        for i, log_data in enumerate(logs):
            if len(log_data) == 4:
                component, level, message, radar_id = log_data
                simple_rich_logger.add_log(component, level, message, radar_id)
            else:
                component, level, message = log_data
                simple_rich_logger.add_log(component, level, message)
            
            # 更新统计
            if component == ComponentType.API:
                simple_rich_logger.increment_stat("api_requests")
            elif component == ComponentType.RADAR_SERVER:
                simple_rich_logger.increment_stat("radar_commands")
            
            time.sleep(3)  # 每3秒添加一条日志
            
            # 动态更新状态
            if i == 5:  # 第6条日志后
                simple_rich_logger.update_radar_stats(3, 3)  # 所有雷达都在线
            elif i == 8:  # 第9条日志后
                simple_rich_logger.update_radar_stats(3, 2)  # 一个雷达离线
        
        # 持续运行展示实时更新
        while True:
            time.sleep(10)
            simple_rich_logger.add_log(ComponentType.SYSTEM, "INFO", "系统心跳检测")
            simple_rich_logger.increment_stat("api_requests")
            
    except KeyboardInterrupt:
        print("\n🛑 演示结束")
        simple_rich_logger.stop()

if __name__ == "__main__":
    demo_simple_rich()
