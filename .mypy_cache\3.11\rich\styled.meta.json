{"data_mtime": 1754379926, "dep_lines": [3, 4, 5, 8, 39, 1, 38, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.measure", "rich.segment", "rich.style", "rich.console", "rich.panel", "typing", "rich", "builtins", "_frozen_importlib", "abc", "enum", "rich.box", "rich.jupyter", "rich.text"], "hash": "d837ab20b9cbdd32f0a3cc1b20f2f70f5c647dab", "id": "rich.styled", "ignore_all": true, "interface_hash": "ee6d0fe7b03fa66f214b985a0f5145ac830939ee", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\styled.py", "plugin_data": null, "size": 1234, "suppressed": [], "version_id": "1.15.0"}