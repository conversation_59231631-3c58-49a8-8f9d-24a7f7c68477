{".class": "MypyFile", "_fullname": "markdown_it.parser_core", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ParserCore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown_it.parser_core.ParserCore", "name": "ParserCore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown_it.parser_core.ParserCore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown_it.parser_core", "mro": ["markdown_it.parser_core.ParserCore", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.parser_core.ParserCore.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown_it.parser_core.ParserCore"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParserCore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.parser_core.ParserCore.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["markdown_it.parser_core.ParserCore", "markdown_it.rules_core.state_core.StateCore"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of ParserCore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ruler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.parser_core.ParserCore.ruler", "name": "ruler", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.parser_core.RuleFuncCoreType"}], "extra_attrs": null, "type_ref": "markdown_it.ruler.Ruler"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown_it.parser_core.ParserCore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown_it.parser_core.ParserCore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RuleFuncCoreType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "markdown_it.parser_core.RuleFuncCoreType", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["markdown_it.rules_core.state_core.StateCore"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Ruler": {".class": "SymbolTableNode", "cross_ref": "markdown_it.ruler.Ruler", "kind": "Gdef"}, "StateCore": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.state_core.StateCore", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_core.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_core.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_core.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_core.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_core.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_core.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_rules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "markdown_it.parser_core._rules", "name": "_rules", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.parser_core.RuleFuncCoreType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "block": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.block.block", "kind": "Gdef"}, "inline": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.inline.inline", "kind": "Gdef"}, "linkify": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.linkify.linkify", "kind": "Gdef"}, "normalize": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.normalize.normalize", "kind": "Gdef"}, "replace": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.replacements.replace", "kind": "Gdef"}, "smartquotes": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.smartquotes.smartquotes", "kind": "Gdef"}, "text_join": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.text_join.text_join", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\parser_core.py"}