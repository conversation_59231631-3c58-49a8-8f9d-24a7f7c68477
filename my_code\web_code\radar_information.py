"""
雷达信息模块
提供雷达设备的详细信息、状态监控、历史记录等功能
"""

from datetime import datetime, timedelta  # 时间戳转换
from bson import ObjectId
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
import my_code.radar_code as radar_code
import logging
from shared_config import (
    get_mongo,
    get_radar_collection,
    get_scene_collection,
    get_user_collection,
)
from type import ApiResponse
from validator import *

# 配置日志
logger = logging.getLogger(__name__)

# 创建蓝图
radar_information = Blueprint("radar_information", __name__)


@radar_information.route("/update_radar_information", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="更新雷达信息")
@handle_database_exceptions
@validate_request("radar_ID")
def web_update_radar_information(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]

    # 详细的调试信息
    radar_count = radar_code.manager.get_radar_count()
    radar_ids = radar_code.manager.get_radar_ids()
    logger.info(f"开始更新雷达信息 - 请求雷达ID: {radar_id}")
    logger.info(f"当前注册雷达数量: {radar_count}")
    logger.info(f"当前注册雷达ID列表: {radar_ids}")

    if radar_code.manager.is_radar_registered(radar_id):
        logger.info(f"[雷达{radar_id}] 雷达已注册或在缓存中，开始查询状态")
        radar = radar_code.manager.get_radar(radar_id)
        if radar:
            result = radar.query_radar_state()
        else:
            # 这种情况不应该发生，因为is_radar_registered已经检查了缓存
            # 但为了健壮性，我们还是添加这个检查
            logger.warning(f"[雷达{radar_id}] 雷达在注册表中但无法获取实例")
            result = "雷达离线"
    else:
        logger.warning(f"[雷达{radar_id}] 雷达未注册或离线")
        result = "雷达离线"

    # result = radar_code.query_radar_state(radar_ID)
    radar_information_collection = get_collection(
        get_mongo(), radar_id, "radar_information"
    )

    results_doc = (
        radar_information_collection.find({"serial_number": {"$gte": 1, "$lte": 49}})
        .not_empty()
        .unwrap()
    )
    radar_information_data = []
    for each_doc in results_doc:
        each_doc_data = {
            "ID": each_doc["serial_number"],
            "label": each_doc["mean"],
            "value": each_doc["data"],
        }
        radar_information_data.append(each_doc_data)
    if result == "success":
        return (
            jsonify(
                {
                    "status": "success",  # 状态信息
                    "message": "查询成功",  # 附加消息
                    "data": radar_information_data,  # 数据
                }
            ),
            200,
        )
    else:
        return (
            jsonify(
                {
                    "status": "warning",  # 状态信息
                    "message": "查询失败，该信息为雷达最后在线时刻的状态",  # 附加消息
                    "data": radar_information_data,  # 数据
                }
            ),
            200,
        )
