{"data_mtime": 1749951571, "dep_lines": [27, 27, 28, 15, 27, 33, 6, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 23, 25, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 25, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._import_utils", "collections.abc", "pydantic._internal", "pydantic.main", "__future__", "dataclasses", "keyword", "sys", "typing", "warnings", "weakref", "collections", "copy", "functools", "inspect", "itertools", "types", "typing_extensions", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal._model_construction"], "hash": "2df1ac8f2d892bf303e0bb7b1c91756f001fe61b", "id": "pydantic._internal._utils", "ignore_all": true, "interface_hash": "5b81be1a5b854a46b91022accf403411231b679d", "mtime": 1749916493, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_utils.py", "plugin_data": null, "size": 15344, "suppressed": [], "version_id": "1.15.0"}