# Technology Stack

## Backend Framework
- **Flask**: Main web framework with CORS support
- **Flask-JWT-Extended**: JWT authentication and authorization
- **Flask-PyMongo**: MongoDB integration

## Database
- **MongoDB**: Primary database for radar data, user management, and configurations
- **PyMongo**: MongoDB driver with custom validation framework
- Individual databases per radar device (named by radar ID)

## Data Processing
- **NumPy**: Numerical computations and array operations
- **SciPy**: Scientific computing (interpolation for image processing)
- **Pandas**: Data manipulation and Excel file processing
- **Matplotlib**: Image visualization and plotting
- **TiffFile**: TIFF image format handling
- **Snappy**: Data compression/decompression

## Validation & Error Handling
- **Pydantic**: Data validation and serialization
- **Returns**: Functional programming patterns for error handling
- Custom validation framework with chain operations

## Networking & Communication
- **Socket**: TCP communication with radar hardware
- **Threading/Multiprocessing**: Concurrent radar management
- **Select**: Non-blocking I/O operations

## Security
- **BCrypt**: Password hashing
- **python-dotenv**: Environment variable management

## Development Tools
- **MyPy**: Static type checking
- **UV**: Python package manager (uv.lock present)

## Common Commands

### Development
```bash
# Install dependencies
uv sync

# Run the application
python app.py

# Type checking
mypy .
```

### Database Operations
- MongoDB runs on `localhost:27017`
- Base database: `base_data`
- Individual radar databases named by radar ID (e.g., `001005AD`)

### Testing
```bash
# Run radar manager tests
python test_radar_manager.py

# Run thread safety tests  
python test_thread_safety.py
```

## Configuration
- Environment-based configuration (development/testing/production)
- Settings in `config.py` with `.env` file support
- Shared configuration in `shared_config.py` to avoid circular imports