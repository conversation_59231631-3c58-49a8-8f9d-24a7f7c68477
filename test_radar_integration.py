"""
测试雷达Rich日志集成
"""

import time
import threading
from simple_rich_logger import simple_rich_logger, ComponentType
from simple_rich_integration import update_radar_stats, add_radar_connection

def test_radar_integration():
    """测试雷达集成功能"""
    print("🎨 测试雷达Rich日志集成...")
    
    try:
        # 启动Rich日志系统
        simple_rich_logger.start()
        
        # 初始化系统状态
        simple_rich_logger.update_component_status(ComponentType.API, "🟢 运行中")
        simple_rich_logger.update_component_status(ComponentType.RADAR_SERVER, "🟢 运行中")
        simple_rich_logger.update_component_status(ComponentType.DATABASE, "🟢 已连接")
        simple_rich_logger.update_component_status(ComponentType.SYSTEM, "🟢 正常运行")
        simple_rich_logger.update_component_status(ComponentType.RADAR_CLIENT, "🔴 未连接")
        
        # 添加初始日志
        simple_rich_logger.add_log(ComponentType.SYSTEM, "INFO", "系统启动完成")
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "INFO", "雷达服务器已启动")
        
        print("✅ 系统初始化完成，等待5秒...")
        time.sleep(5)
        
        # 模拟雷达连接
        print("📡 模拟雷达001连接...")
        add_radar_connection("001", online=True)
        update_radar_stats(1, 1)
        simple_rich_logger.update_component_status(ComponentType.RADAR_CLIENT, "🟢 已连接")
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "INFO", "雷达001连接成功", "001")
        
        time.sleep(3)
        
        # 模拟更多雷达连接
        print("📡 模拟雷达002连接...")
        add_radar_connection("002", online=True)
        update_radar_stats(2, 2)
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "INFO", "雷达002连接成功", "002")
        
        time.sleep(3)
        
        # 模拟雷达003连接
        print("📡 模拟雷达003连接...")
        add_radar_connection("003", online=True)
        update_radar_stats(3, 3)
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "INFO", "雷达003连接成功", "003")
        
        time.sleep(3)
        
        # 模拟一些API请求
        print("🌐 模拟API请求...")
        for i in range(5):
            simple_rich_logger.add_log(ComponentType.API, "INFO", f"处理API请求 #{i+1}")
            simple_rich_logger.increment_stat("api_requests")
            time.sleep(1)
        
        # 模拟雷达命令
        print("📡 模拟雷达命令...")
        from simple_rich_integration import log_radar_command
        
        log_radar_command("001", "查询雷达状态", success=True)
        time.sleep(1)
        log_radar_command("002", "设置场景参数", success=True)
        time.sleep(1)
        log_radar_command("003", "开始工作", success=False)
        time.sleep(1)
        
        # 模拟雷达断开
        print("📡 模拟雷达003断开...")
        add_radar_connection("003", online=False)
        update_radar_stats(3, 2)
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "WARNING", "雷达003连接断开", "003")
        
        time.sleep(3)
        
        # 模拟更多断开
        print("📡 模拟雷达002断开...")
        add_radar_connection("002", online=False)
        update_radar_stats(3, 1)
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "WARNING", "雷达002连接断开", "002")
        
        time.sleep(3)
        
        # 最后一个雷达断开
        print("📡 模拟雷达001断开...")
        add_radar_connection("001", online=False)
        update_radar_stats(3, 0)
        simple_rich_logger.update_component_status(ComponentType.RADAR_CLIENT, "🔴 未连接")
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "WARNING", "雷达001连接断开", "001")
        
        print("✅ 测试完成，继续运行10秒展示效果...")
        time.sleep(10)
        
        print("🛑 测试结束")
        simple_rich_logger.stop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_radar_integration()
