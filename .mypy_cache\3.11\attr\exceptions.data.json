{".class": "MypyFile", "_fullname": "attr.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AttrsAttributeNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.AttrsAttributeNotFoundError", "name": "AttrsAttributeNotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.AttrsAttributeNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.AttrsAttributeNotFoundError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.AttrsAttributeNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.AttrsAttributeNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultAlreadySetError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.DefaultAlreadySetError", "name": "DefaultAlreadySetError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.DefaultAlreadySetError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.DefaultAlreadySetError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.DefaultAlreadySetError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.DefaultAlreadySetError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrozenAttributeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["attr.exceptions.FrozenError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.FrozenAttributeError", "name": "FrozenAttributeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.FrozenAttributeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.FrozenAttributeError", "attr.exceptions.FrozenError", "builtins.AttributeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.FrozenAttributeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.FrozenAttributeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrozenError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.AttributeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.FrozenError", "name": "FrozenError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.FrozenError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.FrozenError", "builtins.AttributeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "attr.exceptions.FrozenError.msg", "name": "msg", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.FrozenError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.FrozenError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrozenInstanceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["attr.exceptions.FrozenError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.FrozenInstanceError", "name": "FrozenInstanceError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.FrozenInstanceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.FrozenInstanceError", "attr.exceptions.FrozenError", "builtins.AttributeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.FrozenInstanceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.FrozenInstanceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotAnAttrsClassError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.NotAnAttrsClassError", "name": "NotAnAttrsClassError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.NotAnAttrsClassError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.NotAnAttrsClassError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.NotAnAttrsClassError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.NotAnAttrsClassError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotCallableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.NotCallableError", "name": "NotCallableError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.NotCallableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.NotCallableError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.exceptions.NotCallableError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "value"], "arg_types": ["attr.exceptions.NotCallableError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NotCallableError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "attr.exceptions.NotCallableError.msg", "name": "msg", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "attr.exceptions.NotCallableError.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.NotCallableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.NotCallableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PythonTooOldError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.PythonTooOldError", "name": "PythonTooOldError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.PythonTooOldError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.PythonTooOldError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.PythonTooOldError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.PythonTooOldError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnannotatedAttributeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "attr.exceptions.UnannotatedAttributeError", "name": "UnannotatedAttributeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "attr.exceptions.UnannotatedAttributeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "attr.exceptions", "mro": ["attr.exceptions.UnannotatedAttributeError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.exceptions.UnannotatedAttributeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "attr.exceptions.UnannotatedAttributeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\attr\\exceptions.pyi"}