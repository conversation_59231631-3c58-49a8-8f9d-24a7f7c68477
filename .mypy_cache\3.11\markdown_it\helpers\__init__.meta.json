{"data_mtime": 1754379924, "dep_lines": [4, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30], "dependencies": ["markdown_it.helpers.parse_link_destination", "markdown_it.helpers.parse_link_label", "markdown_it.helpers.parse_link_title", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "9f9fa07162504c9b1a8381f32c3f106da6a6bf95", "id": "markdown_it.helpers", "ignore_all": true, "interface_hash": "b8ca134ee83fb489eda426b2e8eaed24ac33c75a", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\helpers\\__init__.py", "plugin_data": null, "size": 253, "suppressed": [], "version_id": "1.15.0"}