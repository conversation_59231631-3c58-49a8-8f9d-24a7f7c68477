{"data_mtime": 1754380249, "dep_lines": [5, 6, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["time", "threading", "simple_rich_logger", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "typing"], "hash": "9324e90b327487a27dd538de70761eaef36f902b", "id": "demo_simple_rich", "ignore_all": false, "interface_hash": "d8bd00e3344ab27de8c3331ffd07e63bd75b08ff", "mtime": 1754380248, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\demo_simple_rich.py", "plugin_data": null, "size": 3086, "suppressed": [], "version_id": "1.15.0"}