"""
测试简化Rich日志系统
"""

import time
from simple_rich_logger import simple_rich_logger, ComponentType

def test_rich():
    """测试Rich日志系统"""
    print("🎨 测试简化版Rich日志系统...")
    
    try:
        # 启动系统
        simple_rich_logger.start()
        print("✅ Rich日志系统启动成功")
        
        # 添加一些测试日志
        simple_rich_logger.add_log(ComponentType.API, "INFO", "测试API日志")
        simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "INFO", "测试雷达服务器日志")
        simple_rich_logger.add_log(ComponentType.DATABASE, "INFO", "测试数据库日志")
        simple_rich_logger.add_log(ComponentType.SYSTEM, "INFO", "测试系统日志")
        
        # 更新状态
        simple_rich_logger.update_component_status(ComponentType.API, "🟢 测试中")
        simple_rich_logger.update_component_status(ComponentType.RADAR_SERVER, "🟢 测试中")
        
        # 更新统计
        simple_rich_logger.increment_stat("api_requests", 10)
        simple_rich_logger.increment_stat("radar_commands", 5)
        
        print("✅ 日志添加成功，等待10秒...")
        time.sleep(10)
        
        print("🛑 测试完成，停止系统")
        simple_rich_logger.stop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_rich()
