{"data_mtime": 1754379924, "dep_lines": [12, 12, 12, 12, 12, 12, 21, 11, 12, 7, 9, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["markdown_it.rules_core.block", "markdown_it.rules_core.inline", "markdown_it.rules_core.linkify", "markdown_it.rules_core.normalize", "markdown_it.rules_core.smartquotes", "markdown_it.rules_core.text_join", "markdown_it.rules_core.state_core", "markdown_it.ruler", "markdown_it.rules_core", "__future__", "typing", "builtins", "_frozen_importlib", "abc", "markdown_it.rules_core.replacements"], "hash": "546244e1f1b5601952722056d6c7ff9c0db8b7f3", "id": "markdown_it.parser_core", "ignore_all": true, "interface_hash": "433f40f9a4318bcd2e2e109af5786d5fa136d28f", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\parser_core.py", "plugin_data": null, "size": 1010, "suppressed": [], "version_id": "1.15.0"}