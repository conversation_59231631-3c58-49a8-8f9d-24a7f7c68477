{"data_mtime": 1754379924, "dep_lines": [3, 6, 10, 1, 4, 5, 8, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "urllib.parse", "markdown_it._punycode", "__future__", "contextlib", "re", "mdurl", "markdown_it", "builtins", "_frozen_importlib", "abc", "enum", "typing"], "hash": "efeb60a8890c22778d680380822f4ac00fe9a5af", "id": "markdown_it.common.normalize_url", "ignore_all": true, "interface_hash": "dc87f895254b55c2880f8cbce06902a56e2467b1", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\common\\normalize_url.py", "plugin_data": null, "size": 2568, "suppressed": [], "version_id": "1.15.0"}