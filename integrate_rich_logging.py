"""
Rich日志系统集成模块
将Rich日志美化系统集成到现有的雷达管理项目中
"""

import logging
import atexit
import signal
import sys
from typing import Optional
from rich_logger import (
    rich_logger_system, 
    ComponentType, 
    RichLogHandler,
    setup_rich_logging
)
from rich_config import get_theme, LAYOUT_CONFIG

class RichLoggingIntegrator:
    """Rich日志系统集成器"""
    
    def __init__(self, theme_name: str = "default"):
        self.theme_name = theme_name
        self.original_handlers = {}
        self.integrated = False
        
    def integrate_flask_app(self, app):
        """集成Flask应用日志"""
        if self.integrated:
            return
            
        # 启动Rich日志系统
        rich_logger_system.start()
        
        # 更新系统状态
        rich_logger_system.update_component_status(ComponentType.API, "🟢 运行中")
        rich_logger_system.update_component_status(ComponentType.DATABASE, "🟢 已连接")
        rich_logger_system.update_component_status(ComponentType.SYSTEM, "🟢 正常运行")
        
        # 集成Flask应用日志
        self._setup_flask_logging(app)
        
        # 集成其他模块日志
        self._setup_module_logging()
        
        # 注册退出处理
        self._register_exit_handlers()
        
        self.integrated = True
        
        # 添加启动日志
        rich_logger_system.add_log(
            ComponentType.SYSTEM, 
            "INFO", 
            "🚀 Rich日志系统已成功集成到Flask应用"
        )
    
    def _setup_flask_logging(self, app):
        """设置Flask日志"""
        # 保存原始处理器
        self.original_handlers['flask'] = app.logger.handlers.copy()
        
        # 清除Flask默认处理器
        app.logger.handlers.clear()
        
        # 添加Rich处理器
        rich_handler = RichLogHandler(ComponentType.API)
        rich_handler.setLevel(logging.INFO)
        app.logger.addHandler(rich_handler)
        app.logger.setLevel(logging.INFO)
        
        # 拦截Flask请求日志
        self._setup_flask_request_logging(app)
    
    def _setup_flask_request_logging(self, app):
        """设置Flask请求日志"""
        @app.before_request
        def log_request():
            rich_logger_system.increment_stat("api_requests")
            
        @app.after_request  
        def log_response(response):
            # 记录API请求
            from flask import request
            method = request.method
            path = request.path
            status = response.status_code
            
            if status >= 400:
                level = "ERROR" if status >= 500 else "WARNING"
                rich_logger_system.add_log(
                    ComponentType.API,
                    level,
                    f"{method} {path} - {status}"
                )
            else:
                rich_logger_system.add_log(
                    ComponentType.API,
                    "INFO", 
                    f"{method} {path} - {status}"
                )
            
            return response
    
    def _setup_module_logging(self):
        """设置各模块日志"""
        # 设置共享配置模块日志
        self._setup_logger('shared_config', ComponentType.DATABASE)
        
        # 设置数据库模块日志
        self._setup_logger('database', ComponentType.DATABASE)
        
        # 设置雷达代码模块日志
        self._setup_logger('my_code.radar_code', ComponentType.RADAR_SERVER)
        
        # 设置Web模块日志
        self._setup_logger('my_code.web_code.user', ComponentType.API)
        self._setup_logger('my_code.web_code.radar_manage', ComponentType.API)
        self._setup_logger('my_code.web_code.data_analysis', ComponentType.API)
        self._setup_logger('my_code.web_code.radar_information', ComponentType.API)
        self._setup_logger('my_code.web_code.scene_parameter', ComponentType.API)
        
        # 设置验证器日志
        self._setup_logger('validator', ComponentType.SYSTEM)
    
    def _setup_logger(self, logger_name: str, component: ComponentType):
        """设置指定logger"""
        logger = logging.getLogger(logger_name)
        
        # 保存原始处理器
        self.original_handlers[logger_name] = logger.handlers.copy()
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 添加Rich处理器
        rich_handler = RichLogHandler(component)
        rich_handler.setLevel(logging.INFO)
        logger.addHandler(rich_handler)
        logger.setLevel(logging.INFO)
    
    def _register_exit_handlers(self):
        """注册退出处理器"""
        def cleanup():
            if self.integrated:
                rich_logger_system.add_log(
                    ComponentType.SYSTEM,
                    "INFO", 
                    "🛑 系统正在关闭..."
                )
                rich_logger_system.stop()
        
        # 注册正常退出处理
        atexit.register(cleanup)
        
        # 注册信号处理
        def signal_handler(signum, frame):
            cleanup()
            sys.exit(0)
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def add_radar_connection(self, radar_id: str, online: bool = True, working: bool = False):
        """添加雷达连接"""
        rich_logger_system.update_radar_connection(radar_id, online, working)
        
        status = "连接" if online else "断开"
        rich_logger_system.add_log(
            ComponentType.RADAR_SERVER,
            "INFO",
            f"雷达{radar_id}已{status}",
            radar_id=radar_id
        )
        
        # 更新雷达服务器状态
        if online:
            rich_logger_system.update_component_status(
                ComponentType.RADAR_SERVER, 
                "🟢 有雷达连接"
            )
    
    def remove_radar_connection(self, radar_id: str):
        """移除雷达连接"""
        if radar_id in rich_logger_system.radar_connections:
            del rich_logger_system.radar_connections[radar_id]
            
        rich_logger_system.add_log(
            ComponentType.RADAR_SERVER,
            "WARNING",
            f"雷达{radar_id}连接已断开",
            radar_id=radar_id
        )
        
        # 检查是否还有其他雷达连接
        if not rich_logger_system.radar_connections:
            rich_logger_system.update_component_status(
                ComponentType.RADAR_SERVER,
                "🟡 等待雷达连接"
            )
    
    def start_radar_simulator(self):
        """启动雷达模拟器"""
        rich_logger_system.update_component_status(
            ComponentType.RADAR_SIMULATOR,
            "🟢 运行中"
        )
        rich_logger_system.add_log(
            ComponentType.RADAR_SIMULATOR,
            "INFO",
            "🎯 雷达模拟器已启动"
        )
    
    def stop_radar_simulator(self):
        """停止雷达模拟器"""
        rich_logger_system.update_component_status(
            ComponentType.RADAR_SIMULATOR,
            "🔴 已停止"
        )
        rich_logger_system.add_log(
            ComponentType.RADAR_SIMULATOR,
            "INFO", 
            "🎯 雷达模拟器已停止"
        )
    
    def log_radar_command(self, radar_id: str, command: str, success: bool = True):
        """记录雷达命令"""
        rich_logger_system.increment_stat("radar_commands")
        
        level = "INFO" if success else "ERROR"
        status = "成功" if success else "失败"
        
        rich_logger_system.add_log(
            ComponentType.RADAR_SERVER,
            level,
            f"命令 {command} 执行{status}",
            radar_id=radar_id
        )
    
    def log_data_processing(self, radar_id: str, data_type: str, size: int):
        """记录数据处理"""
        rich_logger_system.increment_stat("data_processed")
        
        rich_logger_system.add_log(
            ComponentType.RADAR_SERVER,
            "INFO",
            f"处理{data_type}数据 ({size} bytes)",
            radar_id=radar_id
        )
    
    def restore_original_logging(self):
        """恢复原始日志配置"""
        if not self.integrated:
            return
            
        # 停止Rich日志系统
        rich_logger_system.stop()
        
        # 恢复原始处理器
        for logger_name, handlers in self.original_handlers.items():
            if logger_name == 'flask':
                # 特殊处理Flask logger
                from flask import current_app
                if current_app:
                    current_app.logger.handlers = handlers
            else:
                logger = logging.getLogger(logger_name)
                logger.handlers = handlers
        
        self.integrated = False

# 全局集成器实例
rich_integrator = RichLoggingIntegrator()

# 便捷函数
def integrate_rich_logging(app, theme_name: str = "default"):
    """集成Rich日志到Flask应用"""
    rich_integrator.integrate_flask_app(app)
    return rich_integrator

def add_radar_connection(radar_id: str, online: bool = True, working: bool = False):
    """添加雷达连接"""
    rich_integrator.add_radar_connection(radar_id, online, working)

def remove_radar_connection(radar_id: str):
    """移除雷达连接"""
    rich_integrator.remove_radar_connection(radar_id)

def log_radar_command(radar_id: str, command: str, success: bool = True):
    """记录雷达命令"""
    rich_integrator.log_radar_command(radar_id, command, success)

def log_data_processing(radar_id: str, data_type: str, size: int):
    """记录数据处理"""
    rich_integrator.log_data_processing(radar_id, data_type, size)

def start_radar_simulator():
    """启动雷达模拟器"""
    rich_integrator.start_radar_simulator()

def stop_radar_simulator():
    """停止雷达模拟器"""
    rich_integrator.stop_radar_simulator()

# 装饰器支持
def rich_log_api_call(func):
    """API调用日志装饰器"""
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            rich_logger_system.add_log(
                ComponentType.API,
                "INFO",
                f"API调用 {func.__name__} 成功"
            )
            return result
        except Exception as e:
            rich_logger_system.add_log(
                ComponentType.API,
                "ERROR", 
                f"API调用 {func.__name__} 失败: {str(e)}"
            )
            raise
    return wrapper

def rich_log_radar_operation(radar_id: str):
    """雷达操作日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                rich_logger_system.add_log(
                    ComponentType.RADAR_SERVER,
                    "INFO",
                    f"雷达操作 {func.__name__} 成功",
                    radar_id=radar_id
                )
                return result
            except Exception as e:
                rich_logger_system.add_log(
                    ComponentType.RADAR_SERVER,
                    "ERROR",
                    f"雷达操作 {func.__name__} 失败: {str(e)}",
                    radar_id=radar_id
                )
                raise
        return wrapper
    return decorator
