{".class": "MypyFile", "_fullname": "markdown_it.rules_inline.html_inline", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HTML_TAG_RE": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.html_re.HTML_TAG_RE", "kind": "Gdef"}, "StateInline": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_inline.state_inline.StateInline", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.html_inline.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.html_inline.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.html_inline.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.html_inline.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.html_inline.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.html_inline.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "html_inline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "silent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_inline.html_inline.html_inline", "name": "html_inline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "silent"], "arg_types": ["markdown_it.rules_inline.state_inline.StateInline", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "html_inline", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isLetter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_inline.html_inline.isLetter", "name": "isLetter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ch"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isLetter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isLinkClose": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.isLinkClose", "kind": "Gdef"}, "isLinkOpen": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.isLinkOpen", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\html_inline.py"}