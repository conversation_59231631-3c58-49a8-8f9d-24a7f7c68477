{"data_mtime": 1754379924, "dep_lines": [5, 6, 7, 1, 3, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 30, 30, 30], "dependencies": ["markdown_it.ruler", "markdown_it.token", "markdown_it.utils", "__future__", "typing", "markdown_it", "builtins", "_frozen_importlib", "abc", "markdown_it.main"], "hash": "63d239662a6269258840065179f690331015e57a", "id": "markdown_it.rules_core.state_core", "ignore_all": true, "interface_hash": "a46b50f797629b8d5c33b7eeeb55f1531e646544", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_core\\state_core.py", "plugin_data": null, "size": 570, "suppressed": [], "version_id": "1.15.0"}