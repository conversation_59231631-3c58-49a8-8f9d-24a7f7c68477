{"data_mtime": 1754379924, "dep_lines": [17, 18, 19, 20, 21, 22, 23, 24, 1, 2, 4, 25, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["attr.converters", "attr.exceptions", "attr.filters", "attr.setters", "attr.validators", "attr._cmp", "attr._typing_compat", "attr._version_info", "enum", "sys", "typing", "attrs", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "386998180d8a0d35b9dbfe94f035cad77519137d", "id": "attr", "ignore_all": true, "interface_hash": "091f905c06de0280a4f527daec50cff711a978d9", "mtime": 1749267919, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\attr\\__init__.pyi", "plugin_data": null, "size": 11281, "suppressed": [], "version_id": "1.15.0"}