{"data_mtime": 1754380136, "dep_lines": [14, 15, 16, 17, 18, 19, 20, 21, 22, 6, 7, 8, 9, 10, 11, 12, 303, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.console", "rich.layout", "rich.panel", "rich.table", "rich.text", "rich.live", "rich.align", "rich.box", "rich.logging", "logging", "threading", "time", "datetime", "typing", "enum", "collections", "re", "builtins", "_frozen_importlib", "_thread", "abc", "rich", "rich.jupyter", "rich.style", "rich.theme", "typing_extensions"], "hash": "477d32091fb7364f65a542125a1e83161d2571b8", "id": "simple_rich_logger", "ignore_all": false, "interface_hash": "46075cc304e8092ff1a4908895636171b812c48b", "mtime": 1754380135, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\simple_rich_logger.py", "plugin_data": null, "size": 10950, "suppressed": [], "version_id": "1.15.0"}