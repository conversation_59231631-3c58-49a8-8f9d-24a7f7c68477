"""
Rich日志美化系统最终演示
展示完整的日志美化效果
"""

import time
import threading
import random
from simple_rich_logger import simple_rich_logger, ComponentType

def simulate_api_requests():
    """模拟API请求"""
    api_endpoints = [
        "/user/login",
        "/radar_manage/list", 
        "/data_analysis/query",
        "/radar_information/status",
        "/scene_parameter/update"
    ]
    
    while True:
        try:
            endpoint = random.choice(api_endpoints)
            status_code = random.choices([200, 201, 400, 404, 500], weights=[70, 10, 10, 5, 5])[0]
            
            if status_code >= 500:
                level = "ERROR"
            elif status_code >= 400:
                level = "WARNING"
            else:
                level = "INFO"
            
            simple_rich_logger.add_log(
                ComponentType.API,
                level,
                f"POST {endpoint} - {status_code}"
            )
            
            simple_rich_logger.increment_stat("api_requests")
            time.sleep(random.uniform(2, 5))
            
        except Exception:
            break

def simulate_radar_operations():
    """模拟雷达操作"""
    radar_ids = ["001", "002", "003"]
    commands = ["START_WORK", "STOP_WORK", "QUERY_STATUS", "SET_PARAM", "GET_DATA"]
    
    # 初始化雷达连接
    simple_rich_logger.update_radar_stats(3, 2)
    
    while True:
        try:
            radar_id = random.choice(radar_ids)
            command = random.choice(commands)
            success = random.choice([True, True, True, False])  # 75%成功率
            
            level = "INFO" if success else "ERROR"
            status = "成功" if success else "失败"
            
            simple_rich_logger.add_log(
                ComponentType.RADAR_SERVER,
                level,
                f"雷达{radar_id} {command} {status}",
                radar_id=radar_id
            )
            
            simple_rich_logger.increment_stat("radar_commands")
            
            # 随机更新雷达连接状态
            if random.random() < 0.1:  # 10%概率更新连接状态
                online_count = random.randint(1, 3)
                simple_rich_logger.update_radar_stats(3, online_count)
            
            time.sleep(random.uniform(3, 7))
            
        except Exception:
            break

def simulate_database_operations():
    """模拟数据库操作"""
    operations = [
        "用户数据查询",
        "雷达配置更新", 
        "场景参数保存",
        "日志数据写入",
        "统计信息更新"
    ]
    
    while True:
        try:
            operation = random.choice(operations)
            success = random.choice([True, True, True, True, False])  # 80%成功率
            
            level = "INFO" if success else "ERROR"
            status = "完成" if success else "失败"
            
            simple_rich_logger.add_log(
                ComponentType.DATABASE,
                level,
                f"{operation}{status}"
            )
            
            time.sleep(random.uniform(4, 8))
            
        except Exception:
            break

def simulate_system_events():
    """模拟系统事件"""
    events = [
        "系统心跳检测",
        "内存使用率检查",
        "磁盘空间监控", 
        "网络连接测试",
        "服务健康检查"
    ]
    
    while True:
        try:
            event = random.choice(events)
            level = random.choices(["INFO", "WARNING"], weights=[90, 10])[0]
            
            simple_rich_logger.add_log(
                ComponentType.SYSTEM,
                level,
                event
            )
            
            time.sleep(random.uniform(8, 15))
            
        except Exception:
            break

def main():
    """主演示函数"""
    print("🎨 Rich日志美化系统完整演示")
    print("=" * 50)
    print("✨ 特性展示:")
    print("  🌈 多彩分区日志显示")
    print("  📡 实时雷达状态监控")
    print("  📊 动态统计信息更新")
    print("  🎯 中文字符完美对齐")
    print("  🔄 稳定无抖动显示")
    print()
    print("🚀 启动演示...")
    print("按 Ctrl+C 退出演示")
    print()
    
    # 启动Rich日志系统
    simple_rich_logger.start()
    
    # 初始化系统状态
    simple_rich_logger.update_component_status(ComponentType.API, "🟢 运行中")
    simple_rich_logger.update_component_status(ComponentType.RADAR_SERVER, "🟢 运行中")
    simple_rich_logger.update_component_status(ComponentType.DATABASE, "🟢 已连接")
    simple_rich_logger.update_component_status(ComponentType.SYSTEM, "🟢 正常运行")
    
    # 添加初始日志
    simple_rich_logger.add_log(ComponentType.SYSTEM, "INFO", "Rich日志系统启动完成")
    simple_rich_logger.add_log(ComponentType.API, "INFO", "Flask应用服务器启动")
    simple_rich_logger.add_log(ComponentType.RADAR_SERVER, "INFO", "雷达服务器初始化完成")
    simple_rich_logger.add_log(ComponentType.DATABASE, "INFO", "MongoDB数据库连接成功")
    
    # 启动模拟线程
    threads = [
        threading.Thread(target=simulate_api_requests, daemon=True),
        threading.Thread(target=simulate_radar_operations, daemon=True),
        threading.Thread(target=simulate_database_operations, daemon=True),
        threading.Thread(target=simulate_system_events, daemon=True)
    ]
    
    for thread in threads:
        thread.start()
    
    try:
        # 主循环 - 保持程序运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 演示结束")
        simple_rich_logger.add_log(ComponentType.SYSTEM, "INFO", "系统正在关闭...")
        time.sleep(2)
        simple_rich_logger.stop()
        print("✅ Rich日志系统已停止")

if __name__ == "__main__":
    main()
