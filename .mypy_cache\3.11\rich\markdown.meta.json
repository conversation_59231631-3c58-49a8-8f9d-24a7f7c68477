{"data_mtime": 1754379926, "dep_lines": [7, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 1, 3, 4, 6, 11, 685, 765, 766, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["markdown_it.token", "rich.table", "rich.box", "rich._loop", "rich._stack", "rich.console", "rich.containers", "rich.jupyter", "rich.panel", "rich.rule", "rich.segment", "rich.style", "rich.syntax", "rich.text", "__future__", "sys", "typing", "markdown_it", "rich", "<PERSON><PERSON><PERSON><PERSON>", "io", "pydoc", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "datetime", "enum", "markdown_it.main", "markdown_it.renderer", "markdown_it.utils", "os", "rich.theme", "types", "typing_extensions"], "hash": "69bb9fed3341460e4d1ffea6f2bb948ddcffe8e6", "id": "rich.markdown", "ignore_all": true, "interface_hash": "3e706e3a0e900267aba71d43b622fa41df9edf44", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\markdown.py", "plugin_data": null, "size": 25846, "suppressed": [], "version_id": "1.15.0"}