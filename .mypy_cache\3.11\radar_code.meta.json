{"data_mtime": 1754380213, "dep_lines": [26, 35, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 27, 29, 30, 31, 32, 33, 35, 36, 38, 39, 40, 55, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 34, 28], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 10, 5, 10, 10, 5, 10, 5, 10, 20, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["pymongo.errors", "matplotlib.pyplot", "socket", "select", "struct", "collections", "logging", "enum", "pymongo", "pandas", "os", "typing", "<PERSON><PERSON><PERSON>", "numpy", "tifffile", "time", "threading", "multiprocessing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "queue", "datetime", "pydantic", "functools", "simple_rich_integration", "builtins", "PIL", "PIL.Image", "_collections_abc", "_frozen_importlib", "_hashlib", "_io", "_sitebuiltins", "_socket", "_thread", "_typeshed", "abc", "annotated_types", "array", "bson", "bson.codec_options", "bson.raw_bson", "io", "matplotlib.artist", "matplotlib.colorizer", "matplotlib.colors", "matplotlib.figure", "matplotlib.image", "mmap", "multiprocessing.context", "multiprocessing.process", "numpy._globals", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "numpy.random", "pandas._libs", "pandas._libs.lib", "pandas._typing", "pandas.core", "pandas.core.arraylike", "pandas.core.arrays", "pandas.core.arrays.base", "pandas.core.base", "pandas.core.dtypes", "pandas.core.dtypes.base", "pandas.core.frame", "pandas.core.generic", "pandas.core.indexes", "pandas.core.indexes.base", "pandas.core.indexing", "pandas.core.series", "pandas.errors", "pandas.io", "pandas.io.excel", "pandas.io.excel._base", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "pymongo.collation", "pymongo.common", "pymongo.results", "pymongo.synchronous", "pymongo.synchronous.client_session", "pymongo.synchronous.collection", "pymongo.synchronous.cursor", "pymongo.synchronous.database", "pymongo.synchronous.mongo_client", "re", "tifffile.tifffile", "types", "typing_extensions"], "hash": "599607f169b803e1a6e6388c2f2a53dc63d96c83", "id": "radar_code", "ignore_all": false, "interface_hash": "454324ad41beba8fd6db77ccf4b94eaaf9432e88", "mtime": 1754380208, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\radar_code.py", "plugin_data": null, "size": 116394, "suppressed": ["scipy.interpolate", "snappy"], "version_id": "1.15.0"}