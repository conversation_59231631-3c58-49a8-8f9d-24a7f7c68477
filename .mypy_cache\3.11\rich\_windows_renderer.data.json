{".class": "MypyFile", "_fullname": "rich._windows_renderer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ControlCode": {".class": "SymbolTableNode", "cross_ref": "rich.segment.ControlCode", "kind": "Gdef"}, "ControlType": {".class": "SymbolTableNode", "cross_ref": "rich.segment.ControlType", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LegacyWindowsTerm": {".class": "SymbolTableNode", "cross_ref": "rich._win32_console.LegacyWindowsTerm", "kind": "Gdef"}, "Segment": {".class": "SymbolTableNode", "cross_ref": "rich.segment.Segment", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "WindowsCoordinates": {".class": "SymbolTableNode", "cross_ref": "rich._win32_console.WindowsCoordinates", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows_renderer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows_renderer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows_renderer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows_renderer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows_renderer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows_renderer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "legacy_windows_render": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["buffer", "term"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich._windows_renderer.legacy_windows_render", "name": "legacy_windows_render", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["buffer", "term"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "rich._win32_console.LegacyWindowsTerm"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "legacy_windows_render", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\_windows_renderer.py"}