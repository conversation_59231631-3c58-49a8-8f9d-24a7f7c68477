{".class": "MypyFile", "_fullname": "attr.validators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Container": {".class": "SymbolTableNode", "cross_ref": "typing.Container", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ContextManager": {".class": "SymbolTableNode", "cross_ref": "typing.ContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Match": {".class": "SymbolTableNode", "cross_ref": "re.Match", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UnionType": {".class": "SymbolTableNode", "cross_ref": "types.UnionType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_I": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._I", "name": "_I", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "values": [], "variance": 0}}, "_K": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._K", "name": "_K", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_M": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._M", "name": "_M", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 0}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "name": "_T1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "name": "_T2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "name": "_T3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_V": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._V", "name": "_V", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_ValidatorArgType": {".class": "SymbolTableNode", "cross_ref": "attrs._ValidatorArgType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ValidatorType": {".class": "SymbolTableNode", "cross_ref": "attrs._ValidatorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.validators.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.validators.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.validators.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.validators.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.validators.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "attr.validators.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "and_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.and_", "name": "and_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.and_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "and_", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.and_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.and_", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "deep_iterable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["member_validator", "iterable_validator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.deep_iterable", "name": "deep_iterable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["member_validator", "iterable_validator"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.deep_iterable", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorArgType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._I", "id": -2, "name": "_I", "namespace": "attr.validators.deep_iterable", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deep_iterable", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._I", "id": -2, "name": "_I", "namespace": "attr.validators.deep_iterable", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.deep_iterable", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._I", "id": -2, "name": "_I", "namespace": "attr.validators.deep_iterable", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "values": [], "variance": 0}]}}}, "deep_mapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["key_validator", "value_validator", "mapping_validator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.deep_mapping", "name": "deep_mapping", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["key_validator", "value_validator", "mapping_validator"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._K", "id": -1, "name": "_K", "namespace": "attr.validators.deep_mapping", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._V", "id": -2, "name": "_V", "namespace": "attr.validators.deep_mapping", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._M", "id": -3, "name": "_M", "namespace": "attr.validators.deep_mapping", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deep_mapping", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._M", "id": -3, "name": "_M", "namespace": "attr.validators.deep_mapping", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._K", "id": -1, "name": "_K", "namespace": "attr.validators.deep_mapping", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._V", "id": -2, "name": "_V", "namespace": "attr.validators.deep_mapping", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._M", "id": -3, "name": "_M", "namespace": "attr.validators.deep_mapping", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 0}]}}}, "disabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.disabled", "name": "disabled", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disabled", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.ContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.ge", "name": "ge", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.ge", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ge", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.ge", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.ge", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_disabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.get_disabled", "name": "get_disabled", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_disabled", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.gt", "name": "gt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.gt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gt", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.gt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.gt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "in_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.in_", "name": "in_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.in_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Container"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.in_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.in_", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "instance_of": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "attr.validators.instance_of", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": ["types.UnionType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "attr.validators.instance_of", "name": "instance_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": ["types.UnionType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.instance_of#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T1", "id": -1, "name": "_T1", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T2", "id": -2, "name": "_T2", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T3", "id": -3, "name": "_T3", "namespace": "attr.validators.instance_of#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": ["types.UnionType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_of", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "is_callable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.is_callable", "name": "is_callable", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_callable", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.is_callable", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.is_callable", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.le", "name": "le", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.le", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "le", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.le", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.le", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "lt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.lt", "name": "lt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.lt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lt", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.lt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.lt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "matches_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["regex", "flags", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.matches_re", "name": "matches_re", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["regex", "flags", "func"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "attr.validators.matches_re", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "attr.validators.matches_re", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "attr.validators.matches_re", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "attr.validators.matches_re", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "attr.validators.matches_re", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches_re", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "attr.validators.matches_re", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "attr.validators.matches_re", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "max_len": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.max_len", "name": "max_len", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["length"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_len", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.max_len", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.max_len", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "min_len": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.min_len", "name": "min_len", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["length"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "min_len", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.min_len", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.min_len", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "not_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["validator", "msg", "exc_types"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.not_", "name": "not_", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["validator", "msg", "exc_types"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.not_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.not_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.not_", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "optional": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["validator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.optional", "name": "optional", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["validator"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.optional", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.optional", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.optional", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optional", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.optional", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.optional", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "or_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.or_", "name": "or_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["validators"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.or_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "or_", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.or_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "attrs._ValidatorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "attr.validators._T", "id": -1, "name": "_T", "namespace": "attr.validators.or_", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "set_disabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["run"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "attr.validators.set_disabled", "name": "set_disabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["run"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_disabled", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\attr\\validators.pyi"}