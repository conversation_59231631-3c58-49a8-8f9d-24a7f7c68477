# Product Overview

This is a **Radar Management and Data Analysis Platform** - a Flask-based backend system for managing radar devices and processing their data.

## Core Functionality

- **Radar Device Management**: Real-time monitoring and control of radar devices via TCP connections
- **User Authentication**: JWT-based authentication with role-based access control
- **Data Processing**: Handles radar image data, deformation analysis, and moving target detection
- **Scene Management**: Configuration and management of radar scanning scenarios
- **File Management**: Handles radar data files, logs, and image processing

## Key Features

- Multi-radar support with individual device databases
- Real-time TCP communication with radar hardware
- Image data processing (polar to cartesian conversion)
- MongoDB-based data storage with validation framework
- RESTful API endpoints for web frontend integration
- Comprehensive logging and error handling

## Target Users

- Radar operators and technicians
- System administrators
- Data analysts working with radar imagery
- Researchers using radar data for analysis