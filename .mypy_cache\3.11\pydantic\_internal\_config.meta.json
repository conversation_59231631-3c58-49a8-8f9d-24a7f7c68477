{"data_mtime": 1749951571, "dep_lines": [28, 14, 17, 18, 19, 20, 29, 1, 3, 4, 5, 6, 14, 15, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 10, 5, 5, 5, 5, 25, 5, 10, 5, 5, 5, 20, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._schema_generation_shared", "pydantic_core.core_schema", "pydantic.aliases", "pydantic.config", "pydantic.errors", "pydantic.warnings", "pydantic.fields", "__future__", "warnings", "contextlib", "re", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "abc", "pydantic._internal._generate_schema", "pydantic._internal._repr"], "hash": "71627e31acd1565f91f373549d3fbd8b0edaf7a5", "id": "pydantic._internal._config", "ignore_all": true, "interface_hash": "b98b0b76e0e1b63e65cd3bcabef3ada0cb60812b", "mtime": 1749916492, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_config.py", "plugin_data": null, "size": 14253, "suppressed": [], "version_id": "1.15.0"}