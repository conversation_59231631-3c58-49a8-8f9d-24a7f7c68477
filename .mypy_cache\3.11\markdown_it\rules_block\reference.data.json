{".class": "MypyFile", "_fullname": "markdown_it.rules_block.reference", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.rules_block.reference.LOGGER", "name": "LOGGER", "type": "logging.Logger"}}, "StateBlock": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_block.state_block.StateBlock", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.reference.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.reference.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.reference.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.reference.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.reference.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_block.reference.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "charCodeAt": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.charCodeAt", "kind": "Gdef"}, "isSpace": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.isSpace", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "normalizeReference": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.normalizeReference", "kind": "Gdef"}, "reference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "startLine", "_endLine", "silent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_block.reference.reference", "name": "reference", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "startLine", "_endLine", "silent"], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reference", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_block\\reference.py"}