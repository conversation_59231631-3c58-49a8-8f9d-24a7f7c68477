{"data_mtime": 1754379926, "dep_lines": [17, 20, 21, 578, 6, 7, 8, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["ctypes.wintypes", "rich.color", "rich.style", "rich.console", "ctypes", "sys", "typing", "time", "builtins", "_ctypes", "_frozen_importlib", "_typeshed", "abc", "datetime", "enum", "functools", "rich.color_triplet", "rich.jupyter", "rich.text", "rich.theme"], "hash": "4f20d1a3e31a9d8ce949fb30096b341f42c54769", "id": "rich._win32_console", "ignore_all": true, "interface_hash": "37ee7f45ca75fa7d1933f7056a155b1b38979296", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\_win32_console.py", "plugin_data": null, "size": 22719, "suppressed": [], "version_id": "1.15.0"}