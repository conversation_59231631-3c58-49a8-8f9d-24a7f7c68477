{"data_mtime": 1754379926, "dep_lines": [25, 1, 2, 16, 65, 68, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 10, 5, 5, 30, 30, 30], "dependencies": ["rich._win32_console", "sys", "dataclasses", "ctypes", "platform", "rich", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "02eceb2fd2d826c2117f0fbc868041174db47d8c", "id": "rich._windows", "ignore_all": true, "interface_hash": "956ea878577a717ea40ac1b8abdd8acb6ea42b31", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\_windows.py", "plugin_data": null, "size": 1901, "suppressed": [], "version_id": "1.15.0"}