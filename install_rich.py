"""
Rich库安装和配置脚本
"""

import subprocess
import sys
import os

def install_rich():
    """安装Rich库及相关依赖"""
    packages = [
        "rich>=13.0.0",  # Rich库主包
        "rich[jupyter]",  # Jupyter支持（可选）
    ]
    
    print("🎨 正在安装Rich日志美化库...")
    
    for package in packages:
        try:
            print(f"📦 安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    print("🎉 Rich库安装完成！")
    return True

def create_demo_script():
    """创建演示脚本"""
    demo_content = '''"""
Rich日志系统演示脚本
运行此脚本查看Rich日志美化效果
"""

import time
import threading
from rich_logger import rich_logger_system, ComponentType

def demo_logs():
    """演示日志输出"""
    # 启动Rich日志系统
    rich_logger_system.start()
    
    # 更新组件状态
    rich_logger_system.update_component_status(ComponentType.API, "🟢 运行中")
    rich_logger_system.update_component_status(ComponentType.RADAR_SERVER, "🟢 运行中")
    rich_logger_system.update_component_status(ComponentType.DATABASE, "🟢 已连接")
    
    # 模拟雷达连接
    rich_logger_system.update_radar_connection("001", True, False)
    rich_logger_system.update_radar_connection("002", True, True)
    rich_logger_system.update_radar_connection("003", False, False)
    
    # 模拟日志输出
    logs = [
        (ComponentType.API, "INFO", "Flask应用启动成功"),
        (ComponentType.API, "INFO", "用户登录成功", None),
        (ComponentType.RADAR_SERVER, "INFO", "雷达服务器启动", None),
        (ComponentType.RADAR_SERVER, "INFO", "雷达001连接成功", "001"),
        (ComponentType.RADAR_SERVER, "INFO", "雷达002开始工作", "002"),
        (ComponentType.RADAR_SERVER, "WARNING", "雷达003连接超时", "003"),
        (ComponentType.DATABASE, "INFO", "数据库连接成功"),
        (ComponentType.API, "INFO", "处理API请求: /radar_manage/list"),
        (ComponentType.RADAR_SERVER, "INFO", "接收雷达数据", "001"),
        (ComponentType.RADAR_SERVER, "ERROR", "雷达003通信失败", "003"),
        (ComponentType.API, "WARNING", "API请求频率过高"),
        (ComponentType.SYSTEM, "INFO", "系统运行正常"),
    ]
    
    print("🎯 Rich日志系统演示开始...")
    print("按 Ctrl+C 退出演示")
    
    try:
        for i, (component, level, message, radar_id) in enumerate(logs):
            rich_logger_system.add_log(component, level, message, radar_id)
            rich_logger_system.increment_stat("api_requests" if component == ComponentType.API else "radar_commands")
            time.sleep(2)
            
            # 动态更新雷达状态
            if i == 5:  # 第6条日志后
                rich_logger_system.update_radar_connection("003", True, False)
                rich_logger_system.add_log(ComponentType.RADAR_SERVER, "INFO", "雷达003重新连接", "003")
        
        # 持续运行以展示实时更新
        while True:
            time.sleep(5)
            rich_logger_system.add_log(ComponentType.SYSTEM, "INFO", "系统心跳检测")
            rich_logger_system.increment_stat("api_requests")
            
    except KeyboardInterrupt:
        print("\\n🛑 演示结束")
        rich_logger_system.stop()

if __name__ == "__main__":
    demo_logs()
'''
    
    with open("demo_rich_logging.py", "w", encoding="utf-8") as f:
        f.write(demo_content)
    
    print("📝 演示脚本已创建: demo_rich_logging.py")

def create_requirements_update():
    """更新requirements.txt"""
    rich_requirements = [
        "rich>=13.0.0",
    ]
    
    requirements_file = "requirements.txt"
    
    # 读取现有requirements
    existing_requirements = []
    if os.path.exists(requirements_file):
        with open(requirements_file, "r", encoding="utf-8") as f:
            existing_requirements = f.read().splitlines()
    
    # 添加Rich相关依赖
    updated_requirements = existing_requirements.copy()
    for req in rich_requirements:
        if not any(req.split(">=")[0] in line for line in existing_requirements):
            updated_requirements.append(req)
    
    # 写入更新后的requirements
    with open(requirements_file, "w", encoding="utf-8") as f:
        f.write("\\n".join(updated_requirements))
        f.write("\\n")
    
    print(f"📋 {requirements_file} 已更新")

def main():
    """主函数"""
    print("🎨 Rich日志美化系统安装程序")
    print("=" * 50)
    
    # 安装Rich库
    if not install_rich():
        print("❌ 安装失败，请检查网络连接或Python环境")
        return
    
    # 创建演示脚本
    create_demo_script()
    
    # 更新requirements.txt
    create_requirements_update()
    
    print("\\n🎉 安装完成！")
    print("\\n📖 使用说明:")
    print("1. 运行 'python demo_rich_logging.py' 查看演示效果")
    print("2. 在你的Flask应用中，Rich日志系统会自动启动")
    print("3. 查看终端中的美化日志输出")
    print("\\n🎯 功能特性:")
    print("- 🌈 多彩分区日志显示")
    print("- 📡 实时雷达状态监控") 
    print("- 📊 系统统计信息")
    print("- 🎨 动态终端界面")
    print("- 🔄 自动刷新显示")

if __name__ == "__main__":
    main()
