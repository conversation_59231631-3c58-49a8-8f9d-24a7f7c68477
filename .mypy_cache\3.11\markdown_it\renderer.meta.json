{"data_mtime": 1754379924, "dep_lines": [14, 10, 15, 16, 8, 11, 12, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30], "dependencies": ["markdown_it.common.utils", "collections.abc", "markdown_it.token", "markdown_it.utils", "__future__", "inspect", "typing", "builtins", "_frozen_importlib", "abc", "types"], "hash": "39b25f31f93c38468c1fd09e36e1186fb65155e4", "id": "markdown_it.renderer", "ignore_all": true, "interface_hash": "6424c7c2bf8daec3c1f99e195e86ecd4d2335e58", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\renderer.py", "plugin_data": null, "size": 9970, "suppressed": [], "version_id": "1.15.0"}