#!/usr/bin/env python3
"""
测试修复后的 RadarManager 功能
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "my_code"))

from radar_code import RadarManager


def test_radar_manager():
    """测试 RadarManager 的基本功能"""
    print("开始测试 RadarManager...")

    # 获取管理器实例
    manager = RadarManager.get_instance()

    # 测试初始状态
    print(f"初始雷达数量: {manager.get_radar_count()}")
    print(f"初始雷达列表: {manager.get_radar_list()}")
    print(f"初始雷达ID列表: {manager.get_radar_ids()}")

    # 模拟注册雷达
    fake_radars = {}
    for i in range(3):
        radar_id = f"TEST_RADAR_{i:02d}"
        fake_radar = f"FakeRadar_{radar_id}"
        fake_radars[radar_id] = fake_radar

        manager.register_radar(radar_id, fake_radar)  # type: ignore
        print(f"注册雷达: {radar_id}")

    # 检查注册后的状态
    print(f"注册后雷达数量: {manager.get_radar_count()}")
    print(f"注册后雷达ID列表: {manager.get_radar_ids()}")

    # 测试查询功能
    for radar_id in fake_radars:
        if manager.is_radar_registered(radar_id):
            radar = manager.get_radar(radar_id)
            print(f"找到雷达 {radar_id}: {radar}")
        else:
            print(f"❌ 雷达 {radar_id} 未找到")

    # 测试注销功能
    for radar_id in list(fake_radars.keys()):
        manager.unregister_radar(radar_id)
        print(f"注销雷达: {radar_id}")

    # 检查最终状态
    print(f"最终雷达数量: {manager.get_radar_count()}")
    print(f"最终雷达列表: {manager.get_radar_list()}")

    if manager.get_radar_count() == 0:
        print("✅ RadarManager 测试通过")
    else:
        print("❌ RadarManager 测试失败")


def test_singleton():
    """测试单例模式"""
    print("\n测试单例模式...")

    manager1 = RadarManager.get_instance()
    manager2 = RadarManager.get_instance()

    if manager1 is manager2:
        print("✅ 单例模式测试通过")
    else:
        print("❌ 单例模式测试失败")


if __name__ == "__main__":
    test_radar_manager()
    test_singleton()
    print("\n所有测试完成。")
