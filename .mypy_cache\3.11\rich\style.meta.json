{"data_mtime": 1754379926, "dep_lines": [7, 8, 9, 10, 1, 2, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.errors", "rich.color", "rich.repr", "rich.terminal_theme", "sys", "functools", "marshal", "random", "typing", "rich", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "rich.color_triplet", "types", "typing_extensions"], "hash": "eff5f4616ebb3262c708b04d478f84eedc2ccd41", "id": "rich.style", "ignore_all": true, "interface_hash": "9b7c6c875cf53c19805a5e56e21ec55b1431c15d", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\style.py", "plugin_data": null, "size": 27059, "suppressed": [], "version_id": "1.15.0"}