"""
简化版Rich日志集成模块
"""

import logging
import atexit
import signal
import sys
from simple_rich_logger import (
    simple_rich_logger,
    ComponentType,
    SimpleRichHandler,
    setup_simple_rich_logging
)

class SimpleRichIntegrator:
    """简化Rich日志集成器"""
    
    def __init__(self):
        self.integrated = False
        self.original_handlers = {}
        
    def integrate_flask_app(self, app):
        """集成Flask应用"""
        if self.integrated:
            return
            
        # 启动Rich日志系统
        simple_rich_logger.start()
        
        # 更新系统状态
        simple_rich_logger.update_component_status(ComponentType.API, "🟢 运行中")
        simple_rich_logger.update_component_status(ComponentType.DATABASE, "🟢 已连接")
        simple_rich_logger.update_component_status(ComponentType.SYSTEM, "🟢 正常运行")
        
        # 设置日志
        self._setup_logging()
        
        # 设置Flask请求日志
        self._setup_flask_request_logging(app)
        
        # 注册退出处理
        self._register_exit_handlers()
        
        self.integrated = True
        simple_rich_logger.add_log(ComponentType.SYSTEM, "INFO", "Rich日志系统集成完成")
    
    def _setup_logging(self):
        """设置各模块日志"""
        # 设置主要模块日志
        modules = [
            ('app', ComponentType.API),
            ('shared_config', ComponentType.DATABASE),
            ('database', ComponentType.DATABASE),
            ('my_code.radar_code', ComponentType.RADAR_SERVER),
            ('my_code.web_code.user', ComponentType.API),
            ('my_code.web_code.radar_manage', ComponentType.API),
            ('my_code.web_code.data_analysis', ComponentType.API),
            ('my_code.web_code.radar_information', ComponentType.API),
            ('my_code.web_code.scene_parameter', ComponentType.API),
            ('validator', ComponentType.SYSTEM),
        ]
        
        for module_name, component in modules:
            self._setup_module_logger(module_name, component)
    
    def _setup_module_logger(self, module_name: str, component: ComponentType):
        """设置模块日志器"""
        logger = logging.getLogger(module_name)
        
        # 保存原始处理器
        self.original_handlers[module_name] = logger.handlers.copy()
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 添加Rich处理器
        handler = SimpleRichHandler(component)
        handler.setLevel(logging.INFO)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    def _setup_flask_request_logging(self, app):
        """设置Flask请求日志"""
        @app.before_request
        def log_request():
            simple_rich_logger.increment_stat("api_requests")
            
        @app.after_request
        def log_response(response):
            from flask import request
            method = request.method
            path = request.path
            status = response.status_code
            
            # 过滤静态文件请求
            if not path.startswith('/static') and not path.startswith('/image'):
                if status >= 400:
                    level = "ERROR" if status >= 500 else "WARNING"
                    simple_rich_logger.add_log(
                        ComponentType.API,
                        level,
                        f"{method} {path} - {status}"
                    )
                else:
                    simple_rich_logger.add_log(
                        ComponentType.API,
                        "INFO",
                        f"{method} {path} - {status}"
                    )
            
            return response
    
    def _register_exit_handlers(self):
        """注册退出处理"""
        def cleanup():
            if self.integrated:
                simple_rich_logger.add_log(ComponentType.SYSTEM, "INFO", "系统正在关闭...")
                simple_rich_logger.stop()
        
        atexit.register(cleanup)
        
        def signal_handler(signum, frame):
            cleanup()
            sys.exit(0)
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def add_radar_connection(self, radar_id: str, online: bool = True):
        """添加雷达连接"""
        simple_rich_logger.add_log(
            ComponentType.RADAR_SERVER,
            "INFO",
            f"雷达{radar_id}{'连接' if online else '断开'}",
            radar_id=radar_id
        )
        
        # 更新雷达服务器状态
        if online:
            simple_rich_logger.update_component_status(
                ComponentType.RADAR_SERVER,
                "🟢 有雷达连接"
            )
    
    def log_radar_command(self, radar_id: str, command: str, success: bool = True):
        """记录雷达命令"""
        simple_rich_logger.increment_stat("radar_commands")
        
        level = "INFO" if success else "ERROR"
        status = "成功" if success else "失败"
        
        simple_rich_logger.add_log(
            ComponentType.RADAR_SERVER,
            level,
            f"命令{command}执行{status}",
            radar_id=radar_id
        )
    
    def start_radar_server(self):
        """启动雷达服务器"""
        simple_rich_logger.update_component_status(
            ComponentType.RADAR_SERVER,
            "🟢 运行中"
        )
        simple_rich_logger.add_log(
            ComponentType.RADAR_SERVER,
            "INFO",
            "雷达服务器已启动"
        )
    
    def start_radar_simulator(self):
        """启动雷达模拟器"""
        simple_rich_logger.add_log(
            ComponentType.RADAR_SIMULATOR,
            "INFO",
            "雷达模拟器已启动"
        )

# 全局集成器
simple_integrator = SimpleRichIntegrator()

# 便捷函数
def integrate_simple_rich_logging(app):
    """集成简化Rich日志"""
    simple_integrator.integrate_flask_app(app)
    return simple_integrator

def add_radar_connection(radar_id: str, online: bool = True):
    """添加雷达连接"""
    simple_integrator.add_radar_connection(radar_id, online)

def log_radar_command(radar_id: str, command: str, success: bool = True):
    """记录雷达命令"""
    simple_integrator.log_radar_command(radar_id, command, success)

def start_radar_server():
    """启动雷达服务器"""
    simple_integrator.start_radar_server()

def start_radar_simulator():
    """启动雷达模拟器"""
    simple_integrator.start_radar_simulator()

def update_radar_stats(total: int, online: int):
    """更新雷达统计"""
    simple_rich_logger.update_radar_stats(total, online)

# 装饰器
def rich_log_api_call(func):
    """API调用日志装饰器"""
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            simple_rich_logger.add_log(
                ComponentType.API,
                "INFO",
                f"API {func.__name__} 成功"
            )
            return result
        except Exception as e:
            simple_rich_logger.add_log(
                ComponentType.API,
                "ERROR",
                f"API {func.__name__} 失败: {str(e)}"
            )
            raise
    return wrapper

def rich_log_radar_operation(radar_id: str):
    """雷达操作日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                simple_rich_logger.add_log(
                    ComponentType.RADAR_SERVER,
                    "INFO",
                    f"雷达操作 {func.__name__} 成功",
                    radar_id=radar_id
                )
                return result
            except Exception as e:
                simple_rich_logger.add_log(
                    ComponentType.RADAR_SERVER,
                    "ERROR",
                    f"雷达操作 {func.__name__} 失败: {str(e)}",
                    radar_id=radar_id
                )
                raise
        return wrapper
    return decorator
