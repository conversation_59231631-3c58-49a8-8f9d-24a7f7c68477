{"data_mtime": 1754379926, "dep_lines": [1, 1, 1, 1, 1, 5, 6, 1], "dep_prios": [5, 30, 30, 30, 30, 5, 5, 30], "dependencies": ["builtins", "_frozen_importlib", "_typeshed", "abc", "typing", "rich.theme", "rich.style", "rich"], "hash": "21ddc9d8b777c2e676d38129319df05b8194def4", "id": "rich_config", "ignore_all": true, "interface_hash": "7afee1cd8e0fff37f8f1a8315dec17afa0ec2a25", "mtime": 1754379220, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\rich_config.py", "plugin_data": null, "size": 8190, "suppressed": [], "version_id": "1.15.0"}