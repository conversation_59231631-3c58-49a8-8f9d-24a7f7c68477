"""
Rich日志系统配置文件
"""

from rich.theme import Theme
from rich.style import Style

# Rich主题配置
RICH_THEME = Theme({
    # 组件颜色
    "api": "cyan",
    "radar_server": "green", 
    "radar_client": "blue",
    "simulator": "magenta",
    "database": "yellow",
    "system": "white",
    
    # 状态颜色
    "online": "bright_green",
    "offline": "bright_red",
    "working": "bright_blue",
    "idle": "yellow",
    "error": "bright_red",
    "warning": "bright_yellow",
    "info": "bright_cyan",
    "debug": "dim blue",
    
    # UI元素
    "header": "bold bright_magenta",
    "panel_border": "bright_blue",
    "table_header": "bold magenta",
    "timestamp": "dim white",
    "radar_id": "bold yellow",
    
    # 特殊效果
    "success": "bold green",
    "failure": "bold red",
    "highlight": "bold bright_white on blue",
})

# 面板样式配置
PANEL_STYLES = {
    "api": {
        "border_style": "cyan",
        "title_style": "bold cyan",
    },
    "radar_server": {
        "border_style": "green", 
        "title_style": "bold green",
    },
    "radar_client": {
        "border_style": "blue",
        "title_style": "bold blue", 
    },
    "simulator": {
        "border_style": "magenta",
        "title_style": "bold magenta",
    },
    "system_status": {
        "border_style": "bright_blue",
        "title_style": "bold bright_blue",
    },
    "statistics": {
        "border_style": "yellow",
        "title_style": "bold yellow",
    }
}

# 日志级别图标和颜色
LOG_LEVEL_CONFIG = {
    "DEBUG": {
        "icon": "🔍",
        "style": "dim blue",
        "priority": 1
    },
    "INFO": {
        "icon": "ℹ️",
        "style": "bright_cyan",
        "priority": 2
    },
    "WARNING": {
        "icon": "⚠️",
        "style": "bright_yellow", 
        "priority": 3
    },
    "ERROR": {
        "icon": "❌",
        "style": "bright_red",
        "priority": 4
    },
    "CRITICAL": {
        "icon": "🚨",
        "style": "bold bright_red",
        "priority": 5
    }
}

# 组件图标配置
COMPONENT_ICONS = {
    "API": "🌐",
    "RADAR_SERVER": "📡", 
    "RADAR_CLIENT": "📱",
    "RADAR_SIMULATOR": "🎯",
    "DATABASE": "🗄️",
    "SYSTEM": "🖥️"
}

# 状态图标配置
STATUS_ICONS = {
    "online": "🟢",
    "offline": "🔴", 
    "working": "🔧",
    "idle": "⏸️",
    "connecting": "🟡",
    "error": "❌",
    "success": "✅",
    "warning": "⚠️"
}

# 动画配置
SPINNER_STYLES = [
    "dots", "dots2", "dots3", "dots4", "dots5", "dots6", "dots7", "dots8", "dots9", "dots10", "dots11", "dots12",
    "line", "line2", "pipe", "simpleDots", "simpleDotsScrolling", "star", "star2", "flip", "hamburger", "growVertical",
    "growHorizontal", "balloon", "balloon2", "noise", "bounce", "boxBounce", "boxBounce2", "triangle", "arc", "circle",
    "squareCorners", "circleQuarters", "circleHalves", "squish", "toggle", "toggle2", "toggle3", "toggle4", "toggle5",
    "toggle6", "toggle7", "toggle8", "toggle9", "toggle10", "toggle11", "toggle12", "toggle13", "arrow", "arrow2",
    "arrow3", "bouncingBar", "bouncingBall", "smiley", "monkey", "hearts", "clock", "earth", "moon", "runner", "pong",
    "shark", "dqpb", "weather", "christmas"
]

# 布局配置
LAYOUT_CONFIG = {
    "header_height": 3,
    "footer_height": 5,
    "left_ratio": 2,
    "right_ratio": 1,
    "logs_ratio": 2,
    "radar_status_ratio": 1,
    "max_logs_display": 10,
    "max_radar_connections": 20,
    "refresh_rate": 1,  # 秒
}

# 消息过滤配置
MESSAGE_FILTERS = {
    "hide_debug_in_production": True,
    "max_message_length": 100,
    "truncate_long_messages": True,
    "highlight_keywords": [
        "错误", "error", "失败", "failure", "异常", "exception",
        "成功", "success", "完成", "complete", "连接", "connect"
    ]
}

# 颜色渐变配置（用于进度条等）
COLOR_GRADIENTS = {
    "health": ["red", "yellow", "green"],
    "performance": ["blue", "cyan", "green"],
    "temperature": ["blue", "green", "yellow", "red"]
}

# 特殊效果配置
EFFECTS_CONFIG = {
    "enable_animations": True,
    "enable_gradients": True,
    "enable_shadows": False,
    "enable_borders": True,
    "enable_icons": True,
    "blink_errors": True,
    "highlight_new_logs": True,
    "fade_old_logs": True
}

# 性能配置
PERFORMANCE_CONFIG = {
    "max_logs_in_memory": 1000,
    "log_cleanup_interval": 300,  # 5分钟
    "max_update_frequency": 10,   # 最大10Hz
    "enable_log_compression": False,
    "async_updates": True
}

# 导出配置
EXPORT_CONFIG = {
    "enable_log_export": True,
    "export_formats": ["txt", "json", "csv"],
    "auto_export_interval": 3600,  # 1小时
    "export_directory": "logs/exports",
    "compress_exports": True
}

# 通知配置
NOTIFICATION_CONFIG = {
    "enable_desktop_notifications": False,
    "enable_sound_alerts": False,
    "critical_error_notifications": True,
    "radar_disconnect_notifications": True,
    "system_status_notifications": False
}

# 快捷键配置
HOTKEYS_CONFIG = {
    "toggle_debug": "d",
    "clear_logs": "c", 
    "export_logs": "e",
    "toggle_animations": "a",
    "help": "h",
    "quit": "q"
}

# 自定义样式函数
def get_component_style(component_name: str) -> dict:
    """获取组件样式"""
    return PANEL_STYLES.get(component_name.lower(), {
        "border_style": "white",
        "title_style": "bold white"
    })

def get_log_level_style(level: str) -> dict:
    """获取日志级别样式"""
    return LOG_LEVEL_CONFIG.get(level.upper(), {
        "icon": "📝",
        "style": "white",
        "priority": 0
    })

def get_status_icon(status: str) -> str:
    """获取状态图标"""
    return STATUS_ICONS.get(status.lower(), "❓")

def get_component_icon(component: str) -> str:
    """获取组件图标"""
    return COMPONENT_ICONS.get(component.upper(), "📦")

# 主题切换功能
AVAILABLE_THEMES = {
    "default": RICH_THEME,
    "dark": Theme({
        "api": "bright_cyan",
        "radar_server": "bright_green",
        "radar_client": "bright_blue", 
        "simulator": "bright_magenta",
        "database": "bright_yellow",
        "system": "bright_white",
        "online": "green",
        "offline": "red",
        "working": "blue",
        "idle": "yellow",
        "error": "red",
        "warning": "yellow",
        "info": "cyan",
        "debug": "blue",
    }),
    "light": Theme({
        "api": "blue",
        "radar_server": "green",
        "radar_client": "purple",
        "simulator": "red",
        "database": "orange3",
        "system": "black",
        "online": "green",
        "offline": "red",
        "working": "blue",
        "idle": "orange3",
        "error": "red",
        "warning": "orange3",
        "info": "blue",
        "debug": "grey50",
    }),
    "neon": Theme({
        "api": "bright_cyan",
        "radar_server": "bright_green",
        "radar_client": "bright_blue",
        "simulator": "bright_magenta", 
        "database": "bright_yellow",
        "system": "bright_white",
        "online": "bright_green",
        "offline": "bright_red",
        "working": "bright_blue",
        "idle": "bright_yellow",
        "error": "bright_red",
        "warning": "bright_yellow",
        "info": "bright_cyan",
        "debug": "bright_black",
    })
}

def get_theme(theme_name: str = "default") -> Theme:
    """获取指定主题"""
    return AVAILABLE_THEMES.get(theme_name, RICH_THEME)

# 配置验证
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查必要的配置项
    if LAYOUT_CONFIG["refresh_rate"] <= 0:
        errors.append("刷新率必须大于0")
    
    if LAYOUT_CONFIG["max_logs_display"] <= 0:
        errors.append("最大日志显示数量必须大于0")
    
    if PERFORMANCE_CONFIG["max_logs_in_memory"] <= 0:
        errors.append("内存中最大日志数量必须大于0")
    
    return errors

# 初始化时验证配置
_config_errors = validate_config()
if _config_errors:
    print("配置错误:")
    for error in _config_errors:
        print(f"  - {error}")
