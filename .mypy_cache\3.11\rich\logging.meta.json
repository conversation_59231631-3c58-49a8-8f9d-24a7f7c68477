{"data_mtime": 1754379926, "dep_lines": [8, 11, 12, 13, 14, 15, 1, 2, 4, 5, 6, 10, 243, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["rich._null_file", "rich._log_render", "rich.console", "rich.highlighter", "rich.text", "rich.traceback", "logging", "datetime", "pathlib", "types", "typing", "rich", "time", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "rich.jupyter", "rich.style"], "hash": "a70bafb4d7a3016e4ae21674743146766806c7e7", "id": "rich.logging", "ignore_all": true, "interface_hash": "61e952b45dfd291ed68967b13ea05fc970b1e548", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\logging.py", "plugin_data": null, "size": 12456, "suppressed": [], "version_id": "1.15.0"}