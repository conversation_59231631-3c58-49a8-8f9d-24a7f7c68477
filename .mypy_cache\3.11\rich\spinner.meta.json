{"data_mtime": 1754379926, "dep_lines": [3, 4, 5, 6, 9, 10, 121, 1, 118, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich._spinners", "rich.measure", "rich.table", "rich.text", "rich.console", "rich.style", "rich.live", "typing", "time", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "rich.box", "rich.jupyter", "rich.segment", "types", "typing_extensions"], "hash": "7093969d521d82c1332e9caaa9d1328a7f85e56f", "id": "rich.spinner", "ignore_all": true, "interface_hash": "a417c6c3b792d686390e692857961adb27161e42", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\spinner.py", "plugin_data": null, "size": 4214, "suppressed": [], "version_id": "1.15.0"}