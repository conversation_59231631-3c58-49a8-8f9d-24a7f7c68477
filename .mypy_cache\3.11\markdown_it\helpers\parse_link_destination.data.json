{".class": "MypyFile", "_fullname": "markdown_it.helpers.parse_link_destination", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "_Result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown_it.helpers.parse_link_destination._Result", "name": "_Result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown_it.helpers.parse_link_destination._Result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown_it.helpers.parse_link_destination", "mro": ["markdown_it.helpers.parse_link_destination._Result", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.helpers.parse_link_destination._Result.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown_it.helpers.parse_link_destination._Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Result", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "markdown_it.helpers.parse_link_destination._Result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.helpers.parse_link_destination._Result.lines", "name": "lines", "type": "builtins.int"}}, "ok": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.helpers.parse_link_destination._Result.ok", "name": "ok", "type": "builtins.bool"}}, "pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.helpers.parse_link_destination._Result.pos", "name": "pos", "type": "builtins.int"}}, "str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.helpers.parse_link_destination._Result.str", "name": "str", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown_it.helpers.parse_link_destination._Result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown_it.helpers.parse_link_destination._Result", "values": [], "variance": 0}, "slots": ["lines", "ok", "pos", "str"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.helpers.parse_link_destination.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.helpers.parse_link_destination.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.helpers.parse_link_destination.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.helpers.parse_link_destination.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.helpers.parse_link_destination.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.helpers.parse_link_destination.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "charCodeAt": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.charCodeAt", "kind": "Gdef"}, "parseLinkDestination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["string", "pos", "maximum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.helpers.parse_link_destination.parseLinkDestination", "name": "parseLinkDestination", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["string", "pos", "maximum"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseLinkDestination", "ret_type": "markdown_it.helpers.parse_link_destination._Result", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unescapeAll": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.unescapeAll", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_destination.py"}