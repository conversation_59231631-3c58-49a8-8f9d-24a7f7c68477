{"data_mtime": 1754379926, "dep_lines": [3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.align", "rich.box", "rich.cells", "rich.jupyter", "rich.measure", "rich.padding", "rich.segment", "rich.style", "rich.text", "rich.console", "typing", "builtins", "_frozen_importlib", "abc", "datetime", "enum", "rich.theme", "types", "typing_extensions"], "hash": "7e64c5168f0714df55a93096f2bbea097ec2b8e1", "id": "rich.panel", "ignore_all": true, "interface_hash": "f9aa63eaf89b03dc9c51397785513774ce8fc123", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\panel.py", "plugin_data": null, "size": 11157, "suppressed": [], "version_id": "1.15.0"}