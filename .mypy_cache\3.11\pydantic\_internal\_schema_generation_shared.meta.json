{"data_mtime": 1749951571, "dep_lines": [13, 14, 15, 7, 9, 12, 3, 5, 7, 1, 1, 1], "dep_prios": [25, 25, 25, 10, 5, 25, 5, 5, 20, 5, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._generate_schema", "pydantic._internal._namespace_utils", "pydantic_core.core_schema", "pydantic.annotated_handlers", "pydantic.json_schema", "__future__", "typing", "pydantic_core", "builtins", "_frozen_importlib", "abc"], "hash": "8c4d813e2a8cef18b1bf2f398db489a9a6593890", "id": "pydantic._internal._schema_generation_shared", "ignore_all": true, "interface_hash": "266cf5b3f68d72a098b67ad879c0dfb8f7262f72", "mtime": 1749916492, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py", "plugin_data": null, "size": 4842, "suppressed": [], "version_id": "1.15.0"}