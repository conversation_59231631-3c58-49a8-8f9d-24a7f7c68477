"""
Rich日志美化系统
为雷达管理系统提供分区域、多彩、实时的终端日志显示
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from collections import deque
from dataclasses import dataclass

from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.live import Live
from rich.columns import Columns
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.tree import Tree
from rich.align import Align
from rich.box import ROUNDED, DOUBLE, SIMPLE, HEAVY
from rich.style import Style
from rich.logging import RichHandler
from rich.traceback import install
from rich.rule import Rule
from rich.status import Status

# 安装Rich的异常处理
install(show_locals=True)


class ComponentType(Enum):
    """系统组件类型"""

    API = "API服务器"
    RADAR_SERVER = "雷达服务器"
    RADAR_CLIENT = "雷达客户端"
    RADAR_SIMULATOR = "雷达模拟器"
    DATABASE = "数据库"
    SYSTEM = "系统"


@dataclass
class LogEntry:
    """日志条目"""

    timestamp: datetime
    component: ComponentType
    level: str
    message: str
    radar_id: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None


class RichLoggerSystem:
    """Rich日志美化系统"""

    def __init__(self, max_logs_per_component: int = 50):
        self.console = Console()
        self.max_logs = max_logs_per_component

        # 各组件的日志缓存
        self.logs: Dict[ComponentType, deque] = {
            component: deque(maxlen=max_logs_per_component)
            for component in ComponentType
        }

        # 系统状态
        self.component_status: Dict[ComponentType, str] = {
            ComponentType.API: "🔴 未启动",
            ComponentType.RADAR_SERVER: "🔴 未启动",
            ComponentType.RADAR_CLIENT: "🔴 未连接",
            ComponentType.RADAR_SIMULATOR: "🔴 未连接",
            ComponentType.DATABASE: "🔴 未连接",
            ComponentType.SYSTEM: "🟡 初始化中",
        }

        # 雷达连接状态
        self.radar_connections: Dict[str, Dict[str, Any]] = {}

        # 统计信息
        self.stats = {
            "api_requests": 0,
            "radar_commands": 0,
            "data_processed": 0,
            "errors": 0,
            "start_time": datetime.now(),
        }

        # 布局和Live显示
        self.layout = self._create_layout()
        self.live = None
        self.running = False
        self.update_thread = None

    def _create_layout(self) -> Layout:
        """创建终端布局"""
        layout = Layout()

        # 主要分区 - 减少header和footer高度
        layout.split_column(
            Layout(name="header", size=2),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=2),
        )

        # 主区域分为左右两部分 - 调整比例
        layout["main"].split_row(
            Layout(name="left", ratio=3), Layout(name="right", ratio=2)
        )

        # 左侧分为上下两部分
        layout["left"].split_column(
            Layout(name="logs", ratio=3), Layout(name="radar_status", ratio=2)
        )

        # 右侧分为系统状态和统计
        layout["right"].split_column(
            Layout(name="system_status", ratio=1), Layout(name="statistics", ratio=1)
        )

        return layout

    def _create_header(self) -> Panel:
        """创建头部面板"""
        title = Text("雷达管理系统 - 实时监控", style="bold cyan")

        header_content = Align.center(title)

        return Panel(
            header_content,
            box=SIMPLE,
            style="cyan",
            title="ArcWeb Backend",
            title_align="center",
        )

    def _create_logs_panel(self) -> Panel:
        """创建日志面板"""
        # 创建多列日志显示
        columns = []

        # API服务器日志
        api_logs = self._format_component_logs(ComponentType.API, "🌐", "cyan")
        columns.append(
            Panel(api_logs, title="API服务器", border_style="cyan", box=ROUNDED)
        )

        # 雷达服务器日志
        radar_server_logs = self._format_component_logs(
            ComponentType.RADAR_SERVER, "📡", "green"
        )
        columns.append(
            Panel(
                radar_server_logs, title="雷达服务器", border_style="green", box=ROUNDED
            )
        )

        return Panel(
            Columns(columns, equal=True, expand=True),
            title="📋 系统日志",
            border_style="white",
            box=HEAVY,
        )

    def _format_component_logs(
        self, component: ComponentType, icon: str, color: str
    ) -> Text:
        """格式化组件日志"""
        text = Text()
        logs = list(self.logs[component])[-10:]  # 显示最近10条

        if not logs:
            text.append(f"{icon} 暂无日志\n", style=f"dim {color}")
            return text

        for log in logs:
            # 时间戳
            time_str = log.timestamp.strftime("%H:%M:%S")
            text.append(f"[{time_str}] ", style="dim")

            # 级别标识
            level_style = self._get_level_style(log.level)
            level_icon = self._get_level_icon(log.level)
            text.append(f"{level_icon} ", style=level_style)

            # 雷达ID（如果有）
            if log.radar_id:
                text.append(f"[雷达{log.radar_id}] ", style="bold yellow")

            # 消息内容
            text.append(f"{log.message}\n", style=color)

        return text

    def _create_radar_status_panel(self) -> Panel:
        """创建雷达状态面板"""
        if not self.radar_connections:
            content = Text("🔍 暂无雷达连接", style="dim")
        else:
            table = Table(show_header=True, header_style="bold magenta", box=SIMPLE)
            table.add_column("雷达ID", style="cyan", width=8)
            table.add_column("状态", width=10)
            table.add_column("工作模式", width=12)
            table.add_column("最后活动", width=12)

            for radar_id, info in self.radar_connections.items():
                status_icon = "🟢" if info.get("online", False) else "🔴"
                status_text = "在线" if info.get("online", False) else "离线"

                work_mode = "🔧 工作中" if info.get("working", False) else "⏸️ 待机"
                last_activity = info.get("last_activity", "未知")

                table.add_row(
                    f"R{radar_id}",
                    f"{status_icon} {status_text}",
                    work_mode,
                    last_activity,
                )

            content = table

        return Panel(
            content, title="📡 雷达连接状态", border_style="green", box=ROUNDED
        )

    def _create_system_status_panel(self) -> Panel:
        """创建系统状态面板"""
        tree = Tree("🖥️ 系统组件", style="bold blue")

        for component, status in self.component_status.items():
            if component == ComponentType.RADAR_SIMULATOR and "🟢" not in status:
                # 模拟器连接后会消失的逻辑
                continue

            branch = tree.add(f"{component.value}")
            branch.add(status)

        return Panel(tree, title="⚙️ 系统状态", border_style="blue", box=ROUNDED)

    def _create_statistics_panel(self) -> Panel:
        """创建统计面板"""
        uptime = datetime.now() - self.stats["start_time"]
        uptime_str = str(uptime).split(".")[0]  # 去掉微秒

        stats_text = Text()
        stats_text.append("📊 运行统计\n\n", style="bold yellow")
        stats_text.append(f"⏱️  运行时间: {uptime_str}\n", style="green")
        stats_text.append(f"🌐 API请求: {self.stats['api_requests']}\n", style="cyan")
        stats_text.append(
            f"📡 雷达命令: {self.stats['radar_commands']}\n", style="blue"
        )
        stats_text.append(
            f"📦 数据处理: {self.stats['data_processed']}\n", style="magenta"
        )
        stats_text.append(f"❌ 错误数量: {self.stats['errors']}\n", style="red")

        return Panel(
            stats_text, title="📈 统计信息", border_style="yellow", box=ROUNDED
        )

    def _create_footer(self) -> Panel:
        """创建底部面板"""
        footer_text = Text()
        footer_text.append("💡 提示: ", style="bold yellow")
        footer_text.append("使用 Ctrl+C 优雅退出系统 | ", style="dim")
        footer_text.append("🔄 自动刷新: 1秒 | ", style="dim")
        footer_text.append(
            f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="dim"
        )

        return Panel(Align.center(footer_text), box=SIMPLE, style="dim")

    def _get_level_style(self, level: str) -> str:
        """获取日志级别样式"""
        styles = {
            "DEBUG": "dim blue",
            "INFO": "green",
            "WARNING": "yellow",
            "ERROR": "red",
            "CRITICAL": "bold red",
        }
        return styles.get(level.upper(), "white")

    def _get_level_icon(self, level: str) -> str:
        """获取日志级别图标"""
        icons = {
            "DEBUG": "🔍",
            "INFO": "ℹ️",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "CRITICAL": "🚨",
        }
        return icons.get(level.upper(), "📝")

    def add_log(
        self,
        component: ComponentType,
        level: str,
        message: str,
        radar_id: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None,
    ):
        """添加日志条目"""
        log_entry = LogEntry(
            timestamp=datetime.now(),
            component=component,
            level=level,
            message=message,
            radar_id=radar_id,
            extra_data=extra_data,
        )

        self.logs[component].append(log_entry)

        # 更新统计
        if level.upper() == "ERROR":
            self.stats["errors"] += 1

    def update_component_status(self, component: ComponentType, status: str):
        """更新组件状态"""
        self.component_status[component] = status

    def update_radar_connection(
        self, radar_id: str, online: bool, working: bool = False
    ):
        """更新雷达连接状态"""
        self.radar_connections[radar_id] = {
            "online": online,
            "working": working,
            "last_activity": datetime.now().strftime("%H:%M:%S"),
        }

    def increment_stat(self, stat_name: str, amount: int = 1):
        """增加统计数据"""
        if stat_name in self.stats:
            self.stats[stat_name] += amount

    def _update_display(self):
        """更新显示内容"""
        while self.running:
            try:
                # 更新各个面板
                self.layout["header"].update(self._create_header())
                self.layout["logs"].update(self._create_logs_panel())
                self.layout["radar_status"].update(self._create_radar_status_panel())
                self.layout["system_status"].update(self._create_system_status_panel())
                self.layout["statistics"].update(self._create_statistics_panel())
                self.layout["footer"].update(self._create_footer())

                time.sleep(1)  # 每秒更新一次
            except Exception as e:
                # 静默处理更新错误，避免影响主程序
                pass

    def start(self):
        """启动Rich日志系统"""
        if self.running:
            return

        self.running = True

        # 启动Live显示
        self.live = Live(
            self.layout, console=self.console, refresh_per_second=1, screen=True
        )

        # 启动更新线程
        self.update_thread = threading.Thread(target=self._update_display, daemon=True)
        self.update_thread.start()

        # 启动Live显示
        self.live.start()

        # 添加启动日志
        self.add_log(ComponentType.SYSTEM, "INFO", "Rich日志系统已启动")

    def stop(self):
        """停止Rich日志系统"""
        if not self.running:
            return

        self.running = False

        if self.live:
            self.live.stop()

        self.add_log(ComponentType.SYSTEM, "INFO", "Rich日志系统已停止")


# 全局日志系统实例
rich_logger_system = RichLoggerSystem()


class RichLogHandler(logging.Handler):
    """Rich日志处理器"""

    def __init__(self, component: ComponentType):
        super().__init__()
        self.component = component

    def emit(self, record):
        """发送日志记录"""
        try:
            # 提取雷达ID（如果存在）
            radar_id = None
            message = record.getMessage()

            # 尝试从消息中提取雷达ID
            import re

            radar_match = re.search(r"\[雷达(\w+)\]", message)
            if radar_match:
                radar_id = radar_match.group(1)

            # 添加到Rich日志系统
            rich_logger_system.add_log(
                component=self.component,
                level=record.levelname,
                message=message,
                radar_id=radar_id,
            )

        except Exception:
            # 静默处理错误，避免影响主程序
            pass


def setup_rich_logging():
    """设置Rich日志系统"""
    # 启动Rich日志系统
    rich_logger_system.start()

    # 返回各组件的logger配置函数
    def get_component_logger(name: str, component: ComponentType) -> logging.Logger:
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)

        # 清除现有处理器
        logger.handlers.clear()

        # 添加Rich处理器
        rich_handler = RichLogHandler(component)
        rich_handler.setLevel(logging.INFO)
        logger.addHandler(rich_handler)

        return logger

    return get_component_logger


# 便捷函数
def get_api_logger(name: str = "api") -> logging.Logger:
    """获取API日志器"""
    setup_func = setup_rich_logging()
    return setup_func(name, ComponentType.API)


def get_radar_server_logger(name: str = "radar_server") -> logging.Logger:
    """获取雷达服务器日志器"""
    setup_func = setup_rich_logging()
    return setup_func(name, ComponentType.RADAR_SERVER)


def get_radar_client_logger(name: str = "radar_client") -> logging.Logger:
    """获取雷达客户端日志器"""
    setup_func = setup_rich_logging()
    return setup_func(name, ComponentType.RADAR_CLIENT)


def get_simulator_logger(name: str = "simulator") -> logging.Logger:
    """获取模拟器日志器"""
    setup_func = setup_rich_logging()
    return setup_func(name, ComponentType.RADAR_SIMULATOR)
