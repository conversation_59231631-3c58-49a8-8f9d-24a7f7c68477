{"data_mtime": 1754379924, "dep_lines": [3, 3, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["markdown_it.presets.commonmark", "markdown_it.presets.default", "markdown_it.presets.zero", "markdown_it.utils", "builtins", "_frozen_importlib", "abc", "types", "typing"], "hash": "0fb5f1379f733d27393e69bc462163574f81e591", "id": "markdown_it.presets", "ignore_all": true, "interface_hash": "b5e28a6a9d1771e45c70b229a3d27718eee29f0e", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\presets\\__init__.py", "plugin_data": null, "size": 970, "suppressed": [], "version_id": "1.15.0"}