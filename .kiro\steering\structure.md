# Project Structure

## Root Level Files
- `app.py` - Main Flask application entry point with route definitions
- `config.py` - Environment-based configuration management
- `database.py` - MongoDB connection singleton
- `shared_config.py` - Shared configuration to avoid circular imports
- `type.py` - Common type definitions (ApiResponse, MongoDocument)
- `pyproject.toml` - Project dependencies and metadata

## Core Modules

### `/my_code/` - Main Application Logic
- `radar_code.py` - Core radar communication and data processing (2800+ lines)
- `data.xlsx` - Configuration data for radar parameters
- `/web_code/` - Flask blueprint modules:
  - `user.py` - User authentication and management
  - `radar_manage.py` - Radar device control endpoints
  - `data_analysis.py` - Data analysis endpoints
  - `radar_information.py` - Radar information management
  - `scene_parameter.py` - Scene configuration management

### `/validator/` - Validation Framework
- `validator_framework.py` - Custom validation system with functional programming patterns
- `extra.py` - Additional validation utilities
- Documentation files in Chinese explaining the validation framework

## Data Storage Structure

### File System Organization
```
/{radar_id}/
├── algorithm_file/     # Algorithm configurations
├── log_file/          # Radar operation logs  
├── radar_file/        # Radar-specific files
└── work_data/         # Mission data
    └── {mission_id}/
        └── image_data/ # Processed radar images
```

### Database Structure
- `base_data` - Main database with collections:
  - `radar` - Radar device registry
  - `users` - User accounts and permissions
  - `scene` - Scene configurations
- `{radar_id}` - Individual radar databases containing:
  - `radar_information` - Device parameters
  - `scene_parameter` - Scanning configurations
  - `img_data_{mission_id}` - Image data per mission
  - `deformation_data_{mission_id}` - Deformation analysis
  - `confidence_data_{mission_id}` - Confidence metrics

## Development & Testing
- `/tmp/` - Temporary files and development iterations
- `test_*.py` - Unit tests for radar manager and thread safety
- `/log/` - Application logs
- `/.venv/` - Python virtual environment
- `/.mypy_cache/` - Type checking cache

## Static Assets
- `/static/images/` - Static image files for web interface

## Key Architectural Patterns

### Blueprint Organization
Each major feature area has its own Flask blueprint in `/my_code/web_code/`:
- Modular route organization
- Consistent error handling with decorators
- JWT authentication integration

### Database Per Radar
- Each radar device gets its own MongoDB database
- Enables data isolation and scalability
- Mission-specific collections for time-series data

### Validation Chain Pattern
- Functional validation using the `returns` library
- Chainable validation operations
- Consistent error handling across the application

### Singleton Patterns
- Database connections via `MongoConnector`
- Radar device management via `RadarManager`
- Thread-safe operations with proper locking