{"data_mtime": 1749951571, "dep_lines": [6, 1, 2, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic.fields", "functools", "typing", "pydantic", "builtins", "_frozen_importlib", "abc", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.main"], "hash": "1ff82dc6c6014893c2992994f8b7aa9c18ce8069", "id": "pydantic._internal._import_utils", "ignore_all": true, "interface_hash": "81f021578f7991c3428fec69c78292af118e1192", "mtime": 1749916492, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py", "plugin_data": null, "size": 402, "suppressed": [], "version_id": "1.15.0"}