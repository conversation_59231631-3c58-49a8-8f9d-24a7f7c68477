{"data_mtime": 1754379924, "dep_lines": [18, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["markdown_it.rules_inline.emphasis", "markdown_it.rules_inline.strikethrough", "markdown_it.rules_inline.autolink", "markdown_it.rules_inline.backticks", "markdown_it.rules_inline.balance_pairs", "markdown_it.rules_inline.entity", "markdown_it.rules_inline.escape", "markdown_it.rules_inline.fragments_join", "markdown_it.rules_inline.html_inline", "markdown_it.rules_inline.image", "markdown_it.rules_inline.link", "markdown_it.rules_inline.linkify", "markdown_it.rules_inline.newline", "markdown_it.rules_inline.state_inline", "markdown_it.rules_inline.text", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "5064a4a05ab0b6e76cef70049d57bac50bd456e6", "id": "markdown_it.rules_inline", "ignore_all": true, "interface_hash": "af5bca1e5a7a590b63e835e8b41bacf988baad64", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\__init__.py", "plugin_data": null, "size": 696, "suppressed": [], "version_id": "1.15.0"}