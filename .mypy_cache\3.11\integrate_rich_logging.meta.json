{"data_mtime": 1754380184, "dep_lines": [6, 7, 8, 9, 10, 11, 17, 84, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["logging", "atexit", "signal", "sys", "typing", "rich_logger", "rich_config", "flask", "builtins", "_frozen_importlib", "abc", "enum", "flask.app", "flask.globals", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold"], "hash": "dab094396d7dd8143778956be06a3c68c8132bf1", "id": "integrate_rich_logging", "ignore_all": true, "interface_hash": "da49462566f3aa6adac0cc4135081dc7f22e4233", "mtime": 1754379370, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\integrate_rich_logging.py", "plugin_data": null, "size": 11543, "suppressed": [], "version_id": "1.15.0"}