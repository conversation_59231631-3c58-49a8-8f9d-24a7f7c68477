"""
用户管理模块
提供用户认证、注册、信息管理等功能
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import check_password_hash
from pymongo import MongoClient
import bcrypt  # 哈希加密
import json
import logging
from shared_config import (
    get_mongo,
    get_radar_collection,
    get_scene_collection,
    get_user_collection,
)
from type import ApiResponse
from validator import *

# 配置日志
logger = logging.getLogger(__name__)

# 创建蓝图
user = Blueprint("user", __name__)


@user.route("/login", methods=["POST", "GET"])
@handle_api_exceptions(info="用户登录")
@handle_database_exceptions
def web_login() -> ApiResponse:
    # 如果是 GET 请求，返回测试用的 token（开发环境）
    if request.method == "GET":
        access_token = create_access_token(
            identity=json.dumps(
                {
                    "username": "24216131",
                    "role": "管理员",
                }
            )
        )
        return jsonify(access_token=access_token, username="admin", role="管理员"), 200

    # POST 请求的正常登录逻辑
    data = request.json
    if not data:
        return jsonify({"status": "error", "message": "请求数据不能为空"}), 400

    username = validate(data.get("username"), "用户名").required().not_empty().unwrap()
    password = validate(data.get("password"), "密码").required().not_empty().unwrap()
    logger.info(f"用户{username}尝试登录")

    # 查询数据库，获取符合条件的文档
    user_info = (
        get_user_collection()
        .find_one({"username": username}, error_message="用户不存在")
        .unwrap()
    )
    logger.info(f"用户{username}的密码为{user_info['password']}")
    if not bcrypt.checkpw(password.encode(), user_info["password"].encode()):
        return jsonify({"status": "error", "message": "密码错误"}), 400

    access_token = create_access_token(
        identity=json.dumps(
            {
                "username": user_info["username"],
                "role": user_info["role"],
            }
        )
    )
    return (
        jsonify(
            access_token=access_token,
            username=user_info["username"],
            role=user_info["role"],
        ),
        200,
    )


@user.route("/register", methods=["POST"])
@handle_api_exceptions(info="用户注册")
@handle_database_exceptions
@validate_request("username", "password")
def web_register(**kwargs) -> ApiResponse:
    username = kwargs["username"]
    password = kwargs["password"]
    salt = bcrypt.gensalt()
    password_hashed = bcrypt.hashpw(password.encode(), salt).decode()  # 进行哈希
    role = "用户"

    # 检查用户是否已经存在
    if get_user_collection().find_one({"username": username}).unwrap():
        return jsonify({"status": "error", "message": "用户已注册"}), 200
    else:
        get_user_collection().insert_one(
            {"username": username, "password": password_hashed, "role": role}
        ).unwrap()
        access_token = create_access_token(
            identity=json.dumps(
                {
                    "username": username,
                    "role": role,
                }
            )
        )
        return jsonify(access_token=access_token, username=username, role=role), 200


@user.route("/change_password", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="修改密码")
@handle_database_exceptions
@validate_request("username", "password")
def web_change_password(**kwargs) -> ApiResponse:
    username = kwargs["username"]
    password = kwargs["password"]
    salt = bcrypt.gensalt()
    password_hashed = bcrypt.hashpw(password.encode(), salt).decode()  # 进行哈希
    get_user_collection().update_one(
        {"username": username}, {"$set": {"password": password_hashed}}
    ).unwrap()
    return jsonify({"status": "success", "message": "密码修改成功"}), 200
