from flask import Flask, request, jsonify, send_file, send_from_directory
from flask_cors import CORS  # type: ignore
from typing import Dict, List, Any, Optional
import logging

# import sys  # 未使用，注释掉
import os
import json
from flask_jwt_extended import jwt_required, get_jwt_identity, JWTManager  # type: ignore
from config import config_by_name  # type: ignore

from validator import *
from type import ApiResponse
from bson import ObjectId

# 配置日志
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 1. 加载配置
logger.info("加载配置")
config_name = os.getenv("FLASK_ENV", "development").lower()
app_config = config_by_name.get(config_name, config_by_name["default"])
app.config.from_object(app_config)
jwt = JWTManager(app)

# 本地模块导入 - 在Flask app配置后导入以避免循环导入
from my_code.web_code.user import user
from my_code.web_code.radar_manage import radar_manage
from my_code.web_code.data_analysis import data_analysis
from my_code.web_code.radar_information import radar_information
from my_code.web_code.scene_parameter import scene_parameter

# 注册蓝图
logger.info("注册蓝图")
app.register_blueprint(user, url_prefix="/user")  # 用户管理模块
app.register_blueprint(radar_manage, url_prefix="/radar_manage")  # 雷达管理模块
app.register_blueprint(data_analysis, url_prefix="/data_analysis")  # 数据分析模块
app.register_blueprint(
    radar_information, url_prefix="/radar_information"
)  # 雷达信息模块
app.register_blueprint(scene_parameter, url_prefix="/scene_parameter")  # 场景参数模块

# 初始化共享配置 - 在蓝图注册后初始化
from shared_config import init_shared_config

init_shared_config(app)


@app.route("/check_all_radar", methods=["GET", "POST"])
@jwt_required()
@handle_api_exceptions(info="获取指定场景下的所有雷达信息")
@handle_database_exceptions
@validate_request("scene_ID")
def web_check_all_radar(**kwargs) -> ApiResponse:
    from shared_config import get_scene_collection, get_radar_collection

    scene_id = kwargs.get("scene_ID")
    radar_id_list = (
        get_scene_collection()
        .find_one({"_id": ObjectId(scene_id)})
        .field("radar_ID")
        .not_empty()
        .unwrap()
    )
    radar_list = []
    for each_id in radar_id_list:
        result = get_radar_collection().find_one({"ID": each_id})
        radar_list.append(
            {
                "ID": each_id,
                "name": result.field("name").unwrap_or(None),
                "coordinates": result.field("radar_coordinates").unwrap_or(None),
            }
        )
    return (
        jsonify(
            {"status": "success", "message": "成功获取雷达列表", "data": radar_list}
        ),
        200,
    )


# 监听雷达状态
@app.route("/listen_radar_state", methods=["POST"])
@jwt_required()  # type: ignore
@handle_api_exceptions(info="监听指定雷达的工作状态")
@handle_database_exceptions
@validate_request("radar_ID")
def web_listen_radar_state(**kwargs):
    """监听指定雷达的工作状态 - 使用新验证框架的简化版本"""
    from shared_config import get_radar_collection

    radar_id = kwargs.get("radar_ID")
    # 使用数据库助手查询雷达
    radar_doc = get_radar_collection().find_one({"ID": radar_id})

    # 构建雷达状态信息
    is_work = radar_doc.field("is_work").unwrap_or(0)
    is_online = radar_doc.field("is_online").unwrap_or(0)
    logger.info(
        f"[雷达{radar_id}] 雷达状态为: is_work={is_work}, is_online={is_online}"
    )
    radar_status = {
        "is_work": str(is_work),
        "status": (
            "offline" if is_online == 0 else "online" if is_work == 0 else "work"
        ),
    }

    return (
        jsonify(
            {"status": "success", "message": "成功获取雷达状态", "data": radar_status}
        ),
        200,
    )


# 图像和文件处理路由
@app.route("/<radar_id>/work_data/<mission_id>/image_data/<filename>")
@handle_api_exceptions(info="获取图像文件")
def get_image(radar_id: str, mission_id: str, filename: str) -> ApiResponse:
    """获取指定雷达任务的图像数据"""
    file_path = f"./{radar_id}/work_data/{mission_id}/image_data/{filename}"
    return send_file(file_path, mimetype="image/png"), 200


@app.route("/download/<path:file_path>", methods=["POST", "GET"])
@handle_api_exceptions(info="文件下载")
def web_download(file_path: str) -> ApiResponse:
    """下载指定路径的文件"""
    normalized_path = os.path.normpath(file_path)
    ALLOWED_BASE_DIR = r"E:/实验室/云平台/react_app/public/source"
    absolute_path = os.path.abspath(os.path.join(ALLOWED_BASE_DIR, normalized_path))
    print("访问文件地址为：", absolute_path)

    if not os.path.exists(absolute_path):
        return jsonify({"status": "error", "message": "文件夹不存在"}), 500
    if not os.path.isfile(absolute_path):
        return jsonify({"status": "error", "message": "文件不存在"}), 500

    return (
        send_file(
            absolute_path,
            as_attachment=True,
            download_name=os.path.basename(absolute_path),
        ),
        200,
    )


# 场景相关路由
@app.route("/get_scene_list", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取当前用户可访问的场景列表")
@handle_database_exceptions
def web_get_scene_list() -> ApiResponse:
    """获取当前用户可访问的场景列表"""
    from shared_config import get_user_collection, get_scene_collection

    identity = get_jwt_identity()
    identity_dict = json.loads(identity)
    user_name = (
        validate(identity_dict["username"], "用户名").required().not_empty().unwrap()
    )
    scene_id_list = (
        get_user_collection()
        .find_one({"username": user_name})
        .field("scene_ID")
        .unwrap()
    )
    # logger.info(scene_id_list)
    scene_info_list = []
    for each_scene_id in scene_id_list:
        result = get_scene_collection().find_one({"_id": ObjectId(each_scene_id)})
        scene_info_list.append(
            {
                "name": result.field("name").unwrap_or(None),
                "background_pack": result.field("background_pack").unwrap_or(None),
                "ID": each_scene_id,
                "coordinates": result.field("coordinates").unwrap_or(None),
            }
        )
    return (
        jsonify(
            {
                "status": "success",
                "message": "成功获取场景信息",
                "data": scene_info_list,
            }
        ),
        200,
    )


@app.route("/image/<path:filename>", methods=["POST", "GET"])
@handle_api_exceptions(info="获取静态图像文件")
def get_image1(filename: str) -> ApiResponse:
    """获取静态图像文件"""
    return send_from_directory("", filename), 200


# 运行测试
if __name__ == "__main__":

    # 启动TCP服务器线程 - 确保与Flask应用在同一进程中
    import threading
    from my_code.radar_code import tcp_server, simulate_radar
    import multiprocessing as mp

    if os.name == "nt":
        mp.set_start_method("spawn", force=True)

    logger.info("启动TCP雷达服务器线程...")
    server_thread = threading.Thread(target=tcp_server, daemon=True)
    server_thread.start()
    logger.info("TCP雷达服务器线程已启动")
    logger.info("启动模拟雷达线程...")
    server_thread = threading.Thread(
        target=simulate_radar, daemon=True, args=("localhost", 1030)
    )
    server_thread.start()
    logger.info("模拟雷达线程已启动")

    # 禁用自动重载以避免TCP服务器线程在进程重启时丢失状态
    app.run(host="0.0.0.0", port=5000, debug=True, use_reloader=False)
    # ----^^^^^^^------------------------------^^^^^^^
