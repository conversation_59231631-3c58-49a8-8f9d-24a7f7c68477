{"data_mtime": 1754379924, "dep_lines": [5, 3, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30], "dependencies": ["markdown_it.rules_inline.state_inline", "__future__", "builtins", "_frozen_importlib", "abc", "markdown_it.ruler", "typing"], "hash": "c726d3a777fbd72d57d4253992af186a4ee1696d", "id": "markdown_it.rules_inline.emphasis", "ignore_all": true, "interface_hash": "b23dc24b680d718759d447a291c13a8e07c706da", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\emphasis.py", "plugin_data": null, "size": 3123, "suppressed": [], "version_id": "1.15.0"}