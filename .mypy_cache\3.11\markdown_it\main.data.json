{".class": "MypyFile", "_fullname": "markdown_it.main", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "EnvType": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.EnvType", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MarkdownIt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown_it.main.MarkdownIt", "name": "MarkdownIt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown_it.main", "mro": ["markdown_it.main.MarkdownIt", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "inline"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_inline.ParserInline", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "inline"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_inline.ParserInline", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "block"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_block.ParserBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "block"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_block.ParserBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "core"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_core.ParserCore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "core"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_core.ParserCore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "renderer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.renderer.RendererProtocol", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "renderer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.renderer.RendererProtocol", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "markdown_it.main.MarkdownIt.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "inline"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_inline.ParserInline", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "block"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_block.ParserBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "core"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.parser_core.ParserCore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "LiteralType", "fallback": "builtins.str", "value": "renderer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": "markdown_it.renderer.RendererProtocol", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MarkdownIt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "config", "options_update", "renderer_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "config", "options_update", "renderer_cls"], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.PresetType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["markdown_it.main.MarkdownIt"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "markdown_it.renderer.RendererProtocol", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MarkdownIt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["markdown_it.main.MarkdownIt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of MarkdownIt", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_render_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "function", "fmt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.add_render_rule", "name": "add_render_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "function", "fmt"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_render_rule of MarkdownIt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.main.MarkdownIt.block", "name": "block", "type": "markdown_it.parser_block.ParserBlock"}}, "configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "presets", "options_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "presets", "options_update"], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.PresetType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure of MarkdownIt", "ret_type": "markdown_it.main.MarkdownIt", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "core": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.main.MarkdownIt.core", "name": "core", "type": "markdown_it.parser_core.ParserCore"}}, "disable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "names", "ignoreInvalid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.disable", "name": "disable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "names", "ignoreInvalid"], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable of MarkdownIt", "ret_type": "markdown_it.main.MarkdownIt", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "names", "ignoreInvalid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.enable", "name": "enable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "names", "ignoreInvalid"], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable of MarkdownIt", "ret_type": "markdown_it.main.MarkdownIt", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_active_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.get_active_rules", "name": "get_active_rules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown_it.main.MarkdownIt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_active_rules of MarkdownIt", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_all_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.get_all_rules", "name": "get_all_rules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown_it.main.MarkdownIt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_rules of MarkdownIt", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "helpers": {".class": "SymbolTableNode", "cross_ref": "markdown_it.helpers", "implicit": true, "kind": "<PERSON><PERSON><PERSON>"}, "inline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.main.MarkdownIt.inline", "name": "inline", "type": "markdown_it.parser_inline.ParserInline"}}, "linkify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.main.MarkdownIt.linkify", "name": "linkify", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "markdown_it.main.linkify_it", "source_any": {".class": "AnyType", "missing_import_name": "markdown_it.main.linkify_it", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "normalizeLink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.normalizeLink", "name": "normalizeLink", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeLink of MarkdownIt", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalizeLinkText": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "link"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.normalizeLinkText", "name": "normalizeLinkText", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "link"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeLinkText of MarkdownIt", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.main.MarkdownIt.options", "name": "options", "type": "markdown_it.utils.OptionsDict"}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of MarkdownIt", "ret_type": {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseInline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.parseInline", "name": "parseInline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseInline of MarkdownIt", "ret_type": {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of MarkdownIt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderInline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.renderInline", "name": "renderInline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "src", "env"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderInline of MarkdownIt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.main.MarkdownIt.renderer", "name": "renderer", "type": "markdown_it.renderer.RendererProtocol"}}, "reset_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "markdown_it.main.MarkdownIt.reset_rules", "name": "reset_rules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown_it.main.MarkdownIt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_rules of MarkdownIt", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "markdown_it.main.MarkdownIt.reset_rules", "name": "reset_rules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown_it.main.MarkdownIt"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_rules of MarkdownIt", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options"], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.OptionsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of MarkdownIt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "plugin", "params", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.use", "name": "use", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "plugin", "params", "options"], "arg_types": ["markdown_it.main.MarkdownIt", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use of MarkdownIt", "ret_type": "markdown_it.main.MarkdownIt", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "utils": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils", "implicit": true, "kind": "<PERSON><PERSON><PERSON>"}, "validateLink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.main.MarkdownIt.validateLink", "name": "validateLink", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["markdown_it.main.MarkdownIt", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validateLink of MarkdownIt", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown_it.main.MarkdownIt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown_it.main.MarkdownIt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "OptionsDict": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.OptionsDict", "kind": "Gdef"}, "OptionsType": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.OptionsType", "kind": "Gdef"}, "ParserBlock": {".class": "SymbolTableNode", "cross_ref": "markdown_it.parser_block.ParserBlock", "kind": "Gdef"}, "ParserCore": {".class": "SymbolTableNode", "cross_ref": "markdown_it.parser_core.ParserCore", "kind": "Gdef"}, "ParserInline": {".class": "SymbolTableNode", "cross_ref": "markdown_it.parser_inline.ParserInline", "kind": "Gdef"}, "PresetType": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.PresetType", "kind": "Gdef"}, "RendererHTML": {".class": "SymbolTableNode", "cross_ref": "markdown_it.renderer.RendererHTML", "kind": "Gdef"}, "RendererProtocol": {".class": "SymbolTableNode", "cross_ref": "markdown_it.renderer.RendererProtocol", "kind": "Gdef"}, "StateCore": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_core.state_core.StateCore", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "markdown_it.token.Token", "kind": "Gdef"}, "_PRESETS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "markdown_it.main._PRESETS", "name": "_PRESETS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.PresetType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.main.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.main.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.main.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.main.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.main.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.main.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "helpers": {".class": "SymbolTableNode", "cross_ref": "markdown_it.helpers", "kind": "Gdef"}, "linkify_it": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "markdown_it.main.linkify_it", "name": "linkify_it", "type": {".class": "AnyType", "missing_import_name": "markdown_it.main.linkify_it", "source_any": null, "type_of_any": 3}}}, "normalize_url": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.normalize_url", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "presets": {".class": "SymbolTableNode", "cross_ref": "markdown_it.presets", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\main.py"}