{"data_mtime": 1749951571, "dep_lines": [35, 35, 35, 35, 43, 8, 31, 33, 35, 36, 37, 38, 39, 40, 3, 5, 6, 7, 9, 10, 11, 12, 14, 15, 27, 29, 31, 32, 3072, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 25, 5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic._internal._internal_dataclass", "pydantic._internal._utils", "pydantic._internal._validators", "pydantic._internal._core_metadata", "collections.abc", "pydantic_core.core_schema", "typing_inspection.introspection", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.errors", "pydantic.json_schema", "pydantic.warnings", "__future__", "base64", "dataclasses", "re", "datetime", "decimal", "enum", "pathlib", "types", "typing", "uuid", "annotated_types", "pydantic_core", "typing_extensions", "pydantic", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "os", "pydantic._internal._repr", "pydantic_core._pydantic_core"], "hash": "823ea6b127e621a5bbc6f63ff21ce310e45f4508", "id": "pydantic.types", "ignore_all": true, "interface_hash": "c5ab33804ae4edfec9646598ffe647cedc9114ad", "mtime": 1749916492, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\types.py", "plugin_data": null, "size": 104781, "suppressed": [], "version_id": "1.15.0"}