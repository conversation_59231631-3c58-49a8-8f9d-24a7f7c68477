{"data_mtime": 1754379924, "dep_lines": [8, 10, 9, 3, 5, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["markdown_it.common.utils", "markdown_it.rules_core.state_core", "markdown_it.token", "__future__", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum", "markdown_it.ruler"], "hash": "405ca6ab827487e0a4c69f40120456f1e3790996", "id": "markdown_it.rules_core.smartquotes", "ignore_all": true, "interface_hash": "c1b90f0bd3438e9dba1f0ece8be993d40335f3fc", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_core\\smartquotes.py", "plugin_data": null, "size": 7443, "suppressed": [], "version_id": "1.15.0"}