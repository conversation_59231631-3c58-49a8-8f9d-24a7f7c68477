{".class": "MypyFile", "_fullname": "rich.logging", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Console": {".class": "SymbolTableNode", "cross_ref": "rich.console.Console", "kind": "Gdef"}, "ConsoleRenderable": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleRenderable", "kind": "Gdef"}, "FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "rich.logging.FORMAT", "name": "FORMAT", "type": "builtins.str"}}, "FormatTimeCallable": {".class": "SymbolTableNode", "cross_ref": "rich._log_render.FormatTimeCallable", "kind": "Gdef"}, "Handler": {".class": "SymbolTableNode", "cross_ref": "logging.Handler", "kind": "Gdef"}, "Highlighter": {".class": "SymbolTableNode", "cross_ref": "rich.highlighter.Highlighter", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LogRecord": {".class": "SymbolTableNode", "cross_ref": "logging.LogRecord", "kind": "Gdef"}, "LogRender": {".class": "SymbolTableNode", "cross_ref": "rich._log_render.LogRender", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "NullFile": {".class": "SymbolTableNode", "cross_ref": "rich._null_file.NullFile", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ReprHighlighter": {".class": "SymbolTableNode", "cross_ref": "rich.highlighter.Repr<PERSON><PERSON>lighter", "kind": "Gdef"}, "RichHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.Handler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.logging.RichHandler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.logging.RichHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.logging", "mro": ["rich.logging.RichHandler", "logging.Handler", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "HIGHLIGHTER_CLASS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "rich.logging.RichHandler.HIGHLIGHTER_CLASS", "name": "HIGHLIGHTER_CLASS", "type": {".class": "TypeType", "item": "rich.highlighter.Highlighter"}}}, "KEYWORDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "rich.logging.RichHandler.KEYWORDS", "name": "KEYWORDS", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "level", "console", "show_time", "omit_repeated_times", "show_level", "show_path", "enable_link_path", "highlighter", "markup", "rich_tracebacks", "tracebacks_width", "tracebacks_code_width", "tracebacks_extra_lines", "tracebacks_theme", "tracebacks_word_wrap", "tracebacks_show_locals", "tracebacks_suppress", "tracebacks_max_frames", "locals_max_length", "locals_max_string", "log_time_format", "keywords"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.logging.RichHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "level", "console", "show_time", "omit_repeated_times", "show_level", "show_path", "enable_link_path", "highlighter", "markup", "rich_tracebacks", "tracebacks_width", "tracebacks_code_width", "tracebacks_extra_lines", "tracebacks_theme", "tracebacks_word_wrap", "tracebacks_show_locals", "tracebacks_suppress", "tracebacks_max_frames", "locals_max_length", "locals_max_string", "log_time_format", "keywords"], "arg_types": ["rich.logging.RichHandler", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["rich.console.Console", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["rich.highlighter.Highlighter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich._log_render.FormatTimeCallable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RichHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log_render": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler._log_render", "name": "_log_render", "type": "rich._log_render.LogRender"}}, "console": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.console", "name": "console", "type": "rich.console.Console"}}, "emit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.logging.RichHandler.emit", "name": "emit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["rich.logging.RichHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emit of RichHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_link_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.enable_link_path", "name": "enable_link_path", "type": "builtins.bool"}}, "get_level_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.logging.RichHandler.get_level_text", "name": "get_level_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["rich.logging.RichHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_level_text of RichHandler", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "highlighter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.highlighter", "name": "highlighter", "type": "rich.highlighter.Highlighter"}}, "keywords": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.keywords", "name": "keywords", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "locals_max_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.locals_max_length", "name": "locals_max_length", "type": "builtins.int"}}, "locals_max_string": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.locals_max_string", "name": "locals_max_string", "type": "builtins.int"}}, "markup": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.markup", "name": "markup", "type": "builtins.bool"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "record", "traceback", "message_renderable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.logging.RichHandler.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "record", "traceback", "message_renderable"], "arg_types": ["rich.logging.RichHandler", "logging.LogRecord", {".class": "UnionType", "items": ["rich.traceback.<PERSON>back", {".class": "NoneType"}], "uses_pep604_syntax": false}, "rich.console.ConsoleRenderable"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Rich<PERSON><PERSON>ler", "ret_type": "rich.console.ConsoleRenderable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "record", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.logging.RichHandler.render_message", "name": "render_message", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "record", "message"], "arg_types": ["rich.logging.RichHandler", "logging.LogRecord", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_message of RichHandler", "ret_type": "rich.console.ConsoleRenderable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rich_tracebacks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.rich_tracebacks", "name": "rich_tracebacks", "type": "builtins.bool"}}, "tracebacks_code_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_code_width", "name": "tracebacks_code_width", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tracebacks_extra_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_extra_lines", "name": "tracebacks_extra_lines", "type": "builtins.int"}}, "tracebacks_max_frames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_max_frames", "name": "tracebacks_max_frames", "type": "builtins.int"}}, "tracebacks_show_locals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_show_locals", "name": "tracebacks_show_locals", "type": "builtins.bool"}}, "tracebacks_suppress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_suppress", "name": "tracebacks_suppress", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "tracebacks_theme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_theme", "name": "tracebacks_theme", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tracebacks_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_width", "name": "tracebacks_width", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tracebacks_word_wrap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.logging.RichHandler.tracebacks_word_wrap", "name": "tracebacks_word_wrap", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.logging.RichHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.logging.RichHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Text": {".class": "SymbolTableNode", "cross_ref": "rich.text.Text", "kind": "Gdef"}, "Traceback": {".class": "SymbolTableNode", "cross_ref": "rich.traceback.<PERSON>back", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.logging.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.logging.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.logging.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.logging.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.logging.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.logging.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "divide": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.logging.divide", "name": "divide", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_console": {".class": "SymbolTableNode", "cross_ref": "rich.get_console", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.logging.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "sleep": {".class": "SymbolTableNode", "cross_ref": "time.sleep", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\logging.py"}