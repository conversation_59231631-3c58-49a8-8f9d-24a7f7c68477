{".class": "MypyFile", "_fullname": "rich.markdown", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BlockQuote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.TextElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.BlockQuote", "name": "BlockQuote", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.BlockQuote", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.BlockQuote", "rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.BlockQuote.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.BlockQuote"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlockQuote", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.BlockQuote.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.BlockQuote", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of BlockQuote", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "elements": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.BlockQuote.elements", "name": "elements", "type": "rich.containers.Renderables"}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.BlockQuote.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.BlockQuote", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of BlockQuote", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.BlockQuote.style_name", "name": "style_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.BlockQuote.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.BlockQuote", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "CodeBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.TextElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.CodeBlock", "name": "CodeBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.CodeBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.CodeBlock", "rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lexer_name", "theme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.CodeBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lexer_name", "theme"], "arg_types": ["rich.markdown.CodeBlock", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CodeBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.CodeBlock.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.CodeBlock", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of CodeBlock", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.CodeBlock.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.CodeBlock"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of CodeBlock", "ret_type": "rich.markdown.CodeBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.CodeBlock.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.CodeBlock"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of CodeBlock", "ret_type": "rich.markdown.CodeBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "lexer_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.CodeBlock.lexer_name", "name": "lexer_name", "type": "builtins.str"}}, "style_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.CodeBlock.style_name", "name": "style_name", "type": "builtins.str"}}, "theme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.CodeBlock.theme", "name": "theme", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.CodeBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.CodeBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Console": {".class": "SymbolTableNode", "cross_ref": "rich.console.Console", "kind": "Gdef"}, "ConsoleOptions": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleOptions", "kind": "Gdef"}, "Heading": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.TextElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.Heading", "name": "Heading", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.Heading", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.Heading", "rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.Heading.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["rich.markdown.Heading", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Heading", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "rich.markdown.Heading.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.Heading", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of Heading", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.Heading.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.Heading"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Heading", "ret_type": "rich.markdown.Heading", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.Heading.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.Heading"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Heading", "ret_type": "rich.markdown.Heading", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.Heading.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["rich.markdown.Heading", "rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_enter of Heading", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Heading.tag", "name": "tag", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.Heading.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.Heading", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HorizontalRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.HorizontalRule", "name": "HorizontalRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.HorizontalRule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.HorizontalRule", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.HorizontalRule.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.HorizontalRule", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of HorizontalRule", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.HorizontalRule.new_line", "name": "new_line", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.HorizontalRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.HorizontalRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.TextElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.ImageItem", "name": "ImageItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.ImageItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.ImageItem", "rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "destination", "hyperlinks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ImageItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "destination", "hyperlinks"], "arg_types": ["rich.markdown.ImageItem", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImageItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ImageItem.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.ImageItem", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of ImageItem", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.ImageItem.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.ImageItem"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ImageItem", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.ImageItem.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.ImageItem"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ImageItem", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "destination": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.ImageItem.destination", "name": "destination", "type": "builtins.str"}}, "hyperlinks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.ImageItem.hyperlinks", "name": "hyperlinks", "type": "builtins.bool"}}, "link": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.ImageItem.link", "name": "link", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "new_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.ImageItem.new_line", "name": "new_line", "type": "builtins.bool"}}, "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ImageItem.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["rich.markdown.ImageItem", "rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_enter of ImageItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.ImageItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.ImageItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "JupyterMixin": {".class": "SymbolTableNode", "cross_ref": "rich.jupyter.Ju<PERSON>", "kind": "Gdef"}, "JustifyMethod": {".class": "SymbolTableNode", "cross_ref": "rich.console.JustifyMethod", "kind": "Gdef"}, "Link": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.TextElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.Link", "name": "Link", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.Link", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.Link", "rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "href"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.<PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "href"], "arg_types": ["rich.markdown.Link", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Link", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.Link.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.Link"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Link", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.Link.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.Link"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Link", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "href": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Link.href", "name": "href", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.Link.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.Link", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.ListElement", "name": "ListElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.ListElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.ListElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "list_type", "list_start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ListElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "list_type", "list_start"], "arg_types": ["rich.markdown.ListElement", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ListElement.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.ListElement", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of ListElement", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.ListElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.ListElement"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ListElement", "ret_type": "rich.markdown.ListElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.ListElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.ListElement"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ListElement", "ret_type": "rich.markdown.ListElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.ListElement.items", "name": "items", "type": {".class": "Instance", "args": ["rich.markdown.ListItem"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "list_start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.ListElement.list_start", "name": "list_start", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "list_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.ListElement.list_type", "name": "list_type", "type": "builtins.str"}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ListElement.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.ListElement", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of ListElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.ListElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.ListElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.TextElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.ListItem", "name": "ListItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.ListItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.ListItem", "rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ListItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.ListItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "elements": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.ListItem.elements", "name": "elements", "type": "rich.containers.Renderables"}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ListItem.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.ListItem", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of ListItem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_bullet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ListItem.render_bullet", "name": "render_bullet", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.ListItem", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_bullet of ListItem", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "console", "options", "number", "last_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.ListItem.render_number", "name": "render_number", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "console", "options", "number", "last_number"], "arg_types": ["rich.markdown.ListItem", "rich.console.Console", "rich.console.ConsoleOptions", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_number of ListItem", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.ListItem.style_name", "name": "style_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.ListItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.ListItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Markdown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.jupyter.Ju<PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.Markdown", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.Markdown", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.Markdown", "rich.jupyter.Ju<PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "markup", "code_theme", "justify", "style", "hyperlinks", "inline_code_lexer", "inline_code_theme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.Markdown.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "markup", "code_theme", "justify", "style", "hyperlinks", "inline_code_lexer", "inline_code_theme"], "arg_types": ["rich.markdown.Markdown", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Markdown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.Markdown.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.Markdown", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of Markdown", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flatten_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.Markdown._flatten_tokens", "name": "_flatten_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "arg_types": ["rich.markdown.Markdown", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_tokens of Markdown", "ret_type": {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code_theme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Markdown.code_theme", "name": "code_theme", "type": "builtins.str"}}, "elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "rich.markdown.Markdown.elements", "name": "elements", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "rich.markdown.MarkdownElement"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "hyperlinks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Markdown.hyperlinks", "name": "hyperlinks", "type": "builtins.bool"}}, "inline_code_lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Markdown.inline_code_lexer", "name": "inline_code_lexer", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "inline_code_theme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Markdown.inline_code_theme", "name": "inline_code_theme", "type": "builtins.str"}}, "inlines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.Markdown.inlines", "name": "inlines", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "justify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.Markdown.justify", "name": "justify", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "markup": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Markdown.markup", "name": "markup", "type": "builtins.str"}}, "parsed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Markdown.parsed", "name": "parsed", "type": {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.Markdown.style", "name": "style", "type": {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.Markdown", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MarkdownContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.MarkdownContext", "name": "MarkdownContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.MarkdownContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "console", "options", "style", "inline_code_lexer", "inline_code_theme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "console", "options", "style", "inline_code_lexer", "inline_code_theme"], "arg_types": ["rich.markdown.MarkdownContext", "rich.console.Console", "rich.console.ConsoleOptions", "rich.style.Style", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MarkdownContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_syntax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.MarkdownContext._syntax", "name": "_syntax", "type": {".class": "UnionType", "items": ["rich.syntax.Syntax", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "console": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.MarkdownContext.console", "name": "console", "type": "rich.console.Console"}}, "current_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.markdown.MarkdownContext.current_style", "name": "current_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_style of MarkdownContext", "ret_type": "rich.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.markdown.MarkdownContext.current_style", "name": "current_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_style of MarkdownContext", "ret_type": "rich.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enter_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownContext.enter_style", "name": "enter_style", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "style_name"], "arg_types": ["rich.markdown.MarkdownContext", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enter_style of MarkdownContext", "ret_type": "rich.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leave_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownContext.leave_style", "name": "leave_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leave_style of MarkdownContext", "ret_type": "rich.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "node_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownContext.on_text", "name": "on_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "node_type"], "arg_types": ["rich.markdown.MarkdownContext", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_text of MarkdownContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.MarkdownContext.options", "name": "options", "type": "rich.console.ConsoleOptions"}}, "stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.MarkdownContext.stack", "name": "stack", "type": {".class": "Instance", "args": ["rich.markdown.MarkdownElement"], "extra_attrs": null, "type_ref": "rich._stack.<PERSON>ack"}}}, "style_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.MarkdownContext.style_stack", "name": "style_stack", "type": "rich.style.StyleStack"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.MarkdownContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.MarkdownContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MarkdownElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.MarkdownElement", "name": "MarkdownElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownElement.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.MarkdownElement", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of MarkdownElement", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.MarkdownElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.MarkdownElement"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of MarkdownElement", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.MarkdownElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.MarkdownElement"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of MarkdownElement", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "rich.markdown.MarkdownElement.new_line", "name": "new_line", "type": "builtins.bool"}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownElement.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.MarkdownElement", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of MarkdownElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownElement.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["rich.markdown.MarkdownElement", "rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_enter of MarkdownElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_leave": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownElement.on_leave", "name": "on_leave", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["rich.markdown.MarkdownElement", "rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_leave of MarkdownElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.MarkdownElement.on_text", "name": "on_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "text"], "arg_types": ["rich.markdown.MarkdownElement", "rich.markdown.MarkdownContext", {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.TextType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_text of MarkdownElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.MarkdownElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.MarkdownElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MarkdownIt": {".class": "SymbolTableNode", "cross_ref": "markdown_it.main.MarkdownIt", "kind": "Gdef"}, "Panel": {".class": "SymbolTableNode", "cross_ref": "rich.panel.Panel", "kind": "Gdef"}, "Paragraph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.TextElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.Paragraph", "name": "Paragraph", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.Paragraph", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.Paragraph", "rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "justify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.Paragraph.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "justify"], "arg_types": ["rich.markdown.Paragraph", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Paragraph", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "rich.markdown.Paragraph.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.Paragraph", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of Paragraph", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.Paragraph.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.Paragraph"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Paragraph", "ret_type": "rich.markdown.Paragraph", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.Paragraph.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.Paragraph"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Paragraph", "ret_type": "rich.markdown.Paragraph", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "justify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.markdown.Paragraph.justify", "name": "justify", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}}}, "style_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.Paragraph.style_name", "name": "style_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.Paragraph.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.Paragraph", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RenderResult": {".class": "SymbolTableNode", "cross_ref": "rich.console.RenderResult", "kind": "Gdef"}, "Renderables": {".class": "SymbolTableNode", "cross_ref": "rich.containers.Renderables", "kind": "Gdef"}, "Rule": {".class": "SymbolTableNode", "cross_ref": "rich.rule.Rule", "kind": "Gdef"}, "Segment": {".class": "SymbolTableNode", "cross_ref": "rich.segment.Segment", "kind": "Gdef"}, "Stack": {".class": "SymbolTableNode", "cross_ref": "rich._stack.<PERSON>ack", "kind": "Gdef"}, "Style": {".class": "SymbolTableNode", "cross_ref": "rich.style.Style", "kind": "Gdef"}, "StyleStack": {".class": "SymbolTableNode", "cross_ref": "rich.style.StyleStack", "kind": "Gdef"}, "Syntax": {".class": "SymbolTableNode", "cross_ref": "rich.syntax.Syntax", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "rich.table.Table", "kind": "Gdef"}, "TableBodyElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.TableBodyElement", "name": "TableBodyElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.TableBodyElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.TableBodyElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableBodyElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.TableBodyElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TableBodyElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableBodyElement.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.TableBodyElement", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of TableBodyElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.TableBodyElement.rows", "name": "rows", "type": {".class": "Instance", "args": ["rich.markdown.TableRowElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.TableBodyElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.TableBodyElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TableDataElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.TableDataElement", "name": "TableDataElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.TableDataElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.TableDataElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "justify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableDataElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "justify"], "arg_types": ["rich.markdown.TableDataElement", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TableDataElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.TableDataElement.content", "name": "content", "type": "rich.text.Text"}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.markdown.TableDataElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.TableDataElement"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TableDataElement", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.markdown.TableDataElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "markdown", "token"], "arg_types": [{".class": "TypeType", "item": "rich.markdown.TableDataElement"}, "rich.markdown.Markdown", "markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TableDataElement", "ret_type": "rich.markdown.MarkdownElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "justify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.TableDataElement.justify", "name": "justify", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}}}, "on_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableDataElement.on_text", "name": "on_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "text"], "arg_types": ["rich.markdown.TableDataElement", "rich.markdown.MarkdownContext", {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.TextType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_text of TableDataElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.TableDataElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.TableDataElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TableElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.TableElement", "name": "TableElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.TableElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.TableElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.TableElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TableElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableElement.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.markdown.TableElement", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of TableElement", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.TableElement.body", "name": "body", "type": {".class": "UnionType", "items": ["rich.markdown.TableBodyElement", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.TableElement.header", "name": "header", "type": {".class": "UnionType", "items": ["rich.markdown.TableHeaderElement", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableElement.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.TableElement", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of TableElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.TableElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.TableElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TableHeaderElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.TableHeaderElement", "name": "TableHeaderElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.TableHeaderElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.TableHeaderElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableHeaderElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.TableHeaderElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TableHeaderElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableHeaderElement.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.TableHeaderElement", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of TableHeaderElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "row": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.TableHeaderElement.row", "name": "row", "type": {".class": "UnionType", "items": ["rich.markdown.TableRowElement", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.TableHeaderElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.TableHeaderElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TableRowElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.TableRowElement", "name": "TableRowElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.TableRowElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.TableRowElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableRowElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.markdown.TableRowElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TableRowElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cells": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.markdown.TableRowElement.cells", "name": "cells", "type": {".class": "Instance", "args": ["rich.markdown.TableDataElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "on_child_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TableRowElement.on_child_close", "name": "on_child_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "child"], "arg_types": ["rich.markdown.TableRowElement", "rich.markdown.MarkdownContext", "rich.markdown.MarkdownElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_child_close of TableRowElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.TableRowElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.TableRowElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Text": {".class": "SymbolTableNode", "cross_ref": "rich.text.Text", "kind": "Gdef"}, "TextElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.TextElement", "name": "TextElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.TextElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.TextElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable", "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TextElement.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["rich.markdown.TextElement", "rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_enter of TextElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_leave": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TextElement.on_leave", "name": "on_leave", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["rich.markdown.TextElement", "rich.markdown.MarkdownContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_leave of TextElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.markdown.TextElement.on_text", "name": "on_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "text"], "arg_types": ["rich.markdown.TextElement", "rich.markdown.MarkdownContext", {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.TextType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_text of TextElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.TextElement.style", "name": "style", "type": "rich.style.Style"}}, "style_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "rich.markdown.TextElement.style_name", "name": "style_name", "type": "builtins.str"}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.TextElement.text", "name": "text", "type": "rich.text.Text"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.TextElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.TextElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextType": {".class": "SymbolTableNode", "cross_ref": "rich.text.TextType", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "markdown_it.token.Token", "kind": "Gdef"}, "UnknownElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.markdown.MarkdownElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.markdown.UnknownElement", "name": "UnknownElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.markdown.UnknownElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.markdown", "mro": ["rich.markdown.UnknownElement", "rich.markdown.MarkdownElement", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.markdown.UnknownElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.markdown.UnknownElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.markdown.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.markdown.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.markdown.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.markdown.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.markdown.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.markdown.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.markdown.args", "name": "args", "type": "argparse.Namespace"}}, "box": {".class": "SymbolTableNode", "cross_ref": "rich.box", "kind": "Gdef"}, "console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.markdown.console", "name": "console", "type": "rich.console.Console"}}, "fileio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.markdown.fileio", "name": "fileio", "type": "_io.StringIO"}}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing.get_args", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "loop_first": {".class": "SymbolTableNode", "cross_ref": "rich._loop.loop_first", "kind": "Gdef"}, "markdown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.markdown.markdown", "name": "markdown", "type": "rich.markdown.Markdown"}}, "markdown_body": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.markdown.markdown_body", "name": "markdown_body", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "markdown_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.markdown.markdown_file", "name": "markdown_file", "type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}}}, "parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.markdown.parser", "name": "parser", "type": "argparse.ArgumentParser"}}, "pydoc": {".class": "SymbolTableNode", "cross_ref": "pydoc", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\markdown.py"}