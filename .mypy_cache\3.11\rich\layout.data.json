{".class": "MypyFile", "_fullname": "rich.layout", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Align": {".class": "SymbolTableNode", "cross_ref": "rich.align.<PERSON>gn", "kind": "Gdef"}, "ColumnSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.layout.Splitter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout.ColumnSplitter", "name": "ColumnSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.layout.ColumnSplitter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.layout", "mro": ["rich.layout.ColumnSplitter", "rich.layout.Splitter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "divide": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "children", "region"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.ColumnSplitter.divide", "name": "divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "children", "region"], "arg_types": ["rich.layout.ColumnSplitter", {".class": "Instance", "args": ["rich.layout.Layout"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide of ColumnSplitter", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tree_icon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.ColumnSplitter.get_tree_icon", "name": "get_tree_icon", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.ColumnSplitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tree_icon of ColumnSplitter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.layout.ColumnSplitter.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.ColumnSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.layout.ColumnSplitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Console": {".class": "SymbolTableNode", "cross_ref": "rich.console.Console", "kind": "Gdef"}, "ConsoleOptions": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleOptions", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout.Layout", "name": "Layout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.layout.Layout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.layout", "mro": ["rich.layout.Layout", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["rich.layout.Layout", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Layout", "ret_type": "rich.layout.Layout", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "renderable", "name", "size", "minimum_size", "ratio", "visible"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "renderable", "name", "size", "minimum_size", "ratio", "visible"], "arg_types": ["rich.layout.Layout", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "rich.layout.Layout.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.layout.Layout", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.__rich_repr__", "name": "__rich_repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_repr__ of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.repr.Result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_children": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.layout.Layout._children", "name": "_children", "type": {".class": "Instance", "args": ["rich.layout.Layout"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout.Layout._lock", "name": "_lock", "type": "_thread.RLock"}}, "_make_region_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout._make_region_map", "name": "_make_region_map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["rich.layout.Layout", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_region_map of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.layout.RegionMap"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.layout.Layout._render_map", "name": "_render_map", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.layout.RenderMap"}}}, "_renderable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout.Layout._renderable", "name": "_renderable", "type": {".class": "UnionType", "items": ["rich.console.ConsoleRenderable", "rich.console.RichCast", "builtins.str"], "uses_pep604_syntax": false}}}, "add_split": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "layouts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.add_split", "name": "add_split", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "layouts"], "arg_types": ["rich.layout.Layout", {".class": "UnionType", "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_split of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.layout.Layout.children", "name": "children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "children of Layout", "ret_type": {".class": "Instance", "args": ["rich.layout.Layout"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.layout.Layout.children", "name": "children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "children of Layout", "ret_type": {".class": "Instance", "args": ["rich.layout.Layout"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["rich.layout.Layout", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Layout", "ret_type": {".class": "UnionType", "items": ["rich.layout.Layout", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.layout.Layout.map", "name": "map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.layout.RenderMap"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.layout.Layout.map", "name": "map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.layout.RenderMap"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "minimum_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout.Layout.minimum_size", "name": "minimum_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout.Layout.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout.Layout.ratio", "name": "ratio", "type": "builtins.int"}}, "refresh_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "layout_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.refresh_screen", "name": "refresh_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "layout_name"], "arg_types": ["rich.layout.Layout", "rich.console.Console", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refresh_screen of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.layout.Layout", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.layout.RenderMap"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.layout.Layout.renderable", "name": "renderable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderable of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.layout.Layout.renderable", "name": "renderable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderable of Layout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout.Layout.size", "name": "size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "split": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "layouts", "splitter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "layouts", "splitter"], "arg_types": ["rich.layout.Layout", {".class": "UnionType", "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["rich.layout.Splitter", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "layouts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.split_column", "name": "split_column", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "layouts"], "arg_types": ["rich.layout.Layout", {".class": "UnionType", "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_column of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split_row": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "layouts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.split_row", "name": "split_row", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "layouts"], "arg_types": ["rich.layout.Layout", {".class": "UnionType", "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_row of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "splitter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.layout.Layout.splitter", "name": "splitter", "type": "rich.layout.Splitter"}}, "splitters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.layout.Layout.splitters", "name": "splitters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["rich.layout.ColumnSplitter"], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "rich.layout.Splitter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.layout.Layout.tree", "name": "tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tree of Layout", "ret_type": "rich.tree.Tree", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.layout.Layout.tree", "name": "tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tree of Layout", "ret_type": "rich.tree.Tree", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unsplit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.unsplit", "name": "unsplit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsplit of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "renderable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.Layout.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderable"], "arg_types": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of Layout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visible": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout.Layout.visible", "name": "visible", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.Layout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.layout.Layout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LayoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout.LayoutError", "name": "LayoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.layout.LayoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.layout", "mro": ["rich.layout.LayoutError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.layout.LayoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LayoutRender": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout.LayoutRender", "name": "LayoutRender", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "rich.layout.LayoutRender", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["region", "render"]}}, "module_name": "rich.layout", "mro": ["rich.layout.LayoutRender", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.layout.LayoutRender.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.layout.LayoutRender.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.layout.LayoutRender.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "region"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "render"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "region", "render"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "rich.layout.LayoutRender.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "region", "render"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of LayoutRender", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.LayoutRender._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of LayoutRender", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.layout.LayoutRender._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.layout.LayoutRender._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.layout.LayoutRender._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.layout.LayoutRender._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of LayoutRender", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "rich.layout.LayoutRender._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of LayoutRender", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "region", "render"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.LayoutRender._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "region", "render"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of LayoutRender", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender._NT", "id": -1, "name": "_NT", "namespace": "rich.layout.LayoutRender._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.layout.LayoutRender._source", "name": "_source", "type": "builtins.str"}}, "region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.layout.LayoutRender.region", "name": "region", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}}}, "region-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.layout.LayoutRender.region", "kind": "<PERSON><PERSON><PERSON>"}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.layout.LayoutRender.render", "name": "render", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "render-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.layout.LayoutRender.render", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.LayoutRender.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": "rich.layout.LayoutRender"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NoSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.layout.LayoutError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout.No<PERSON><PERSON><PERSON>ter", "name": "NoSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.layout.No<PERSON><PERSON><PERSON>ter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.layout", "mro": ["rich.layout.No<PERSON><PERSON><PERSON>ter", "rich.layout.LayoutError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.NoSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.layout.No<PERSON><PERSON><PERSON>ter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Panel": {".class": "SymbolTableNode", "cross_ref": "rich.panel.Panel", "kind": "Gdef"}, "Pretty": {".class": "SymbolTableNode", "cross_ref": "rich.pretty.Pretty", "kind": "Gdef"}, "RLock": {".class": "SymbolTableNode", "cross_ref": "threading.RLock", "kind": "Gdef"}, "Region": {".class": "SymbolTableNode", "cross_ref": "rich.region.Region", "kind": "Gdef"}, "RegionMap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.layout.RegionMap", "line": 39, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "RenderMap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.layout.RenderMap", "line": 40, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.layout.LayoutRender"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "RenderResult": {".class": "SymbolTableNode", "cross_ref": "rich.console.RenderResult", "kind": "Gdef"}, "RenderableType": {".class": "SymbolTableNode", "cross_ref": "rich.console.RenderableType", "kind": "Gdef"}, "ReprHighlighter": {".class": "SymbolTableNode", "cross_ref": "rich.highlighter.Repr<PERSON><PERSON>lighter", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "rich.repr.Result", "kind": "Gdef"}, "RowSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.layout.Splitter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout.RowSplitter", "name": "RowSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.layout.RowSplitter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.layout", "mro": ["rich.layout.RowSplitter", "rich.layout.Splitter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "divide": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "children", "region"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.RowSplitter.divide", "name": "divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "children", "region"], "arg_types": ["rich.layout.RowSplitter", {".class": "Instance", "args": ["rich.layout.Layout"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide of RowSplitter", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tree_icon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout.RowSplitter.get_tree_icon", "name": "get_tree_icon", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.RowSplitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tree_icon of Row<PERSON>plitter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.layout.RowSplitter.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.RowSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.layout.RowSplitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Segment": {".class": "SymbolTableNode", "cross_ref": "rich.segment.Segment", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Splitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["divide", 1], ["get_tree_icon", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout.Splitter", "name": "Splitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "rich.layout.Splitter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.layout", "mro": ["rich.layout.Splitter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "divide": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "children", "region"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "rich.layout.Splitter.divide", "name": "divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "children", "region"], "arg_types": ["rich.layout.Splitter", {".class": "Instance", "args": ["rich.layout.Layout"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide of Splitter", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "rich.layout.Splitter.divide", "name": "divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "children", "region"], "arg_types": ["rich.layout.Splitter", {".class": "Instance", "args": ["rich.layout.Layout"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divide of Splitter", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_tree_icon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "rich.layout.Splitter.get_tree_icon", "name": "get_tree_icon", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Splitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tree_icon of Splitter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "rich.layout.Splitter.get_tree_icon", "name": "get_tree_icon", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.layout.Splitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tree_icon of Splitter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.layout.Splitter.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout.Splitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.layout.Splitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StyleType": {".class": "SymbolTableNode", "cross_ref": "rich.style.StyleType", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tree": {".class": "SymbolTableNode", "cross_ref": "rich.tree.Tree", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_Placeholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.layout._Placeholder", "name": "_Placeholder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.layout._Placeholder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.layout", "mro": ["rich.layout._Placeholder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "layout", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout._Placeholder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "layout", "style"], "arg_types": ["rich.layout._Placeholder", "rich.layout.Layout", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Placeholder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.layout._Placeholder.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.layout._Placeholder", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of _Placeholder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "highlighter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.layout._Placeholder.highlighter", "name": "highlighter", "type": "rich.highlighter.Repr<PERSON><PERSON>lighter"}}, "layout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout._Placeholder.layout", "name": "layout", "type": "rich.layout.Layout"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.layout._Placeholder.style", "name": "style", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.layout._Placeholder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.layout._Placeholder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.layout.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.layout.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.layout.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.layout.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.layout.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.layout.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.layout.console", "name": "console", "type": "rich.console.Console"}}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "itemgetter": {".class": "SymbolTableNode", "cross_ref": "operator.itemgetter", "kind": "Gdef"}, "layout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.layout.layout", "name": "layout", "type": "rich.layout.Layout"}}, "ratio_resolve": {".class": "SymbolTableNode", "cross_ref": "rich._ratio.ratio_resolve", "kind": "Gdef"}, "rich_repr": {".class": "SymbolTableNode", "cross_ref": "rich.repr.rich_repr", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\layout.py"}