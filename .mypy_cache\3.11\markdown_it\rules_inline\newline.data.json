{".class": "MypyFile", "_fullname": "markdown_it.rules_inline.newline", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "StateInline": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_inline.state_inline.StateInline", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.newline.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.newline.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.newline.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.newline.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.newline.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.rules_inline.newline.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "charStrAt": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.charStrAt", "kind": "Gdef"}, "isStrSpace": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.isStrSpace", "kind": "Gdef"}, "newline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "silent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.rules_inline.newline.newline", "name": "newline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "silent"], "arg_types": ["markdown_it.rules_inline.state_inline.StateInline", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "newline", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\newline.py"}