{"data_mtime": 1754379926, "dep_lines": [3, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 1, 3, 4, 5, 6, 7, 8, 9, 869, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23, 24, 25, 26, 27, 39], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5], "dependencies": ["os.path", "rich.containers", "rich.padding", "rich._loop", "rich.cells", "rich.color", "rich.console", "rich.jupyter", "rich.measure", "rich.segment", "rich.style", "rich.text", "__future__", "os", "re", "sys", "textwrap", "abc", "pathlib", "typing", "<PERSON><PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "_typeshed", "datetime", "enum", "rich.color_triplet", "rich.theme", "types", "typing_extensions"], "hash": "63282bd83c7fce03c24377bf6e6fedce250f4fac", "id": "rich.syntax", "ignore_all": true, "interface_hash": "d63980d1df0be1cb64821a775762ffdfd2b56747", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\syntax.py", "plugin_data": null, "size": 36263, "suppressed": ["pygments.lexer", "pygments.lexers", "pygments.style", "pygments.styles", "pygments.token", "pygments.util"], "version_id": "1.15.0"}