{"data_mtime": 1754379926, "dep_lines": [6, 7, 8, 9, 10, 11, 12, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.color", "rich.color_triplet", "rich.console", "rich.jupyter", "rich.measure", "rich.segment", "rich.style", "math", "functools", "time", "typing", "builtins", "_frozen_importlib", "abc", "datetime", "enum", "rich.text", "rich.theme"], "hash": "67d2ad11833cc754dea867849dd18e10b0c53dfa", "id": "rich.progress_bar", "ignore_all": true, "interface_hash": "8339629de2342c45bd8b7a2402d24dd4ac34bde4", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\progress_bar.py", "plugin_data": null, "size": 8162, "suppressed": [], "version_id": "1.15.0"}