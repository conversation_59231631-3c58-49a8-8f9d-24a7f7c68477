{".class": "MypyFile", "_fullname": "markdown_it.parser_block", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "EnvType": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.EnvType", "kind": "Gdef"}, "LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "markdown_it.parser_block.LOGGER", "name": "LOGGER", "type": "logging.Logger"}}, "MarkdownIt": {".class": "SymbolTableNode", "cross_ref": "markdown_it.main.MarkdownIt", "kind": "Gdef"}, "ParserBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown_it.parser_block.ParserBlock", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown_it.parser_block.ParserBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown_it.parser_block", "mro": ["markdown_it.parser_block.ParserBlock", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.parser_block.ParserBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown_it.parser_block.ParserBlock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParserBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "src", "md", "env", "outTokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.parser_block.ParserBlock.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "src", "md", "env", "outTokens"], "arg_types": ["markdown_it.parser_block.ParserBlock", "builtins.str", "markdown_it.main.MarkdownIt", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}, {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of ParserBlock", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ruler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.parser_block.ParserBlock.ruler", "name": "ruler", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.parser_block.RuleFuncBlockType"}], "extra_attrs": null, "type_ref": "markdown_it.ruler.Ruler"}}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "startLine", "endLine"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.parser_block.ParserBlock.tokenize", "name": "tokenize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "startLine", "endLine"], "arg_types": ["markdown_it.parser_block.ParserBlock", "markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tokenize of Parser<PERSON>lock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown_it.parser_block.ParserBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown_it.parser_block.ParserBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RuleFuncBlockType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "markdown_it.parser_block.RuleFuncBlockType", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["markdown_it.rules_block.state_block.StateBlock", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Ruler": {".class": "SymbolTableNode", "cross_ref": "markdown_it.ruler.Ruler", "kind": "Gdef"}, "StateBlock": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_block.state_block.StateBlock", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "markdown_it.token.Token", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_block.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_block.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_block.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_block.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_block.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.parser_block.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_rules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "markdown_it.parser_block._rules", "name": "_rules", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.parser_block.RuleFuncBlockType"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "rules_block": {".class": "SymbolTableNode", "cross_ref": "markdown_it.rules_block", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\parser_block.py"}