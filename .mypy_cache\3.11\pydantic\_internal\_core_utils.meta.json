{"data_mtime": 1754379962, "dep_lines": [13, 14, 5, 8, 11, 13, 1, 3, 4, 6, 8, 10, 11, 1, 1, 1, 17, 1], "dep_prios": [10, 5, 5, 10, 10, 20, 5, 10, 10, 5, 5, 5, 20, 5, 30, 30, 25, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "collections.abc", "pydantic_core.core_schema", "typing_inspection.typing_objects", "pydantic._internal", "__future__", "inspect", "os", "typing", "pydantic_core", "typing_extensions", "typing_inspection", "builtins", "_frozen_importlib", "abc", "rich.console", "rich"], "hash": "d4d3e76479b41002db9360d237b47d8555726825", "id": "pydantic._internal._core_utils", "ignore_all": true, "interface_hash": "3dd2aeb21ba5ef4817448eef3ba1ac497c818e84", "mtime": 1749916492, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py", "plugin_data": null, "size": 6746, "suppressed": [], "version_id": "1.15.0"}