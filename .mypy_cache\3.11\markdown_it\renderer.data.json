{".class": "MypyFile", "_fullname": "markdown_it.renderer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "EnvType": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.EnvType", "kind": "Gdef"}, "OptionsDict": {".class": "SymbolTableNode", "cross_ref": "markdown_it.utils.OptionsDict", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "RendererHTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown_it.renderer.RendererProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown_it.renderer.RendererHTML", "name": "RendererHTML", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "markdown_it.renderer", "mro": ["markdown_it.renderer.RendererHTML", "markdown_it.renderer.RendererProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "parser"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RendererHTML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__output__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "markdown_it.renderer.RendererHTML.__output__", "name": "__output__", "type": "builtins.str"}}, "code_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.code_block", "name": "code_block", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "code_block of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code_inline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.code_inline", "name": "code_inline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "code_inline of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.fence", "name": "fence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fence of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hardbreak": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.hardbreak", "name": "hardbreak", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hardbreak of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "html_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.html_block", "name": "html_block", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "html_block of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "html_inline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.html_inline", "name": "html_inline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "html_inline of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.image", "name": "image", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "image of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderAttrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "markdown_it.renderer.RendererHTML.renderAttrs", "name": "renderAttrs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["token"], "arg_types": ["markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderAttrs of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "markdown_it.renderer.RendererHTML.renderAttrs", "name": "renderAttrs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["token"], "arg_types": ["markdown_it.token.Token"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderAttrs of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "renderInline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.renderInline", "name": "renderInline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderInline of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderInlineAsText": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.renderInlineAsText", "name": "renderInlineAsText", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "UnionType", "items": [{".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderInlineAsText of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderToken": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.renderToken", "name": "renderToken", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderToken of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rules": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "markdown_it.renderer.RendererHTML.rules", "name": "rules", "type": {".class": "Instance", "args": ["builtins.str", "types.MethodType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "softbreak": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.softbreak", "name": "softbreak", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "softbreak of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown_it.renderer.RendererHTML.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tokens", "idx", "options", "env"], "arg_types": ["markdown_it.renderer.RendererHTML", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of RendererHTML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown_it.renderer.RendererHTML.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown_it.renderer.RendererHTML", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendererProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__output__", 1], ["render", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown_it.renderer.RendererProtocol", "name": "RendererProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "markdown_it.renderer.RendererProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "markdown_it.renderer", "mro": ["markdown_it.renderer.RendererProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__output__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "markdown_it.renderer.RendererProtocol.__output__", "name": "__output__", "type": "builtins.str"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "markdown_it.renderer.RendererProtocol.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tokens", "options", "env"], "arg_types": ["markdown_it.renderer.RendererProtocol", {".class": "Instance", "args": ["markdown_it.token.Token"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "markdown_it.utils.OptionsDict", {".class": "TypeAliasType", "args": [], "type_ref": "markdown_it.utils.EnvType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of RendererProtocol", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown_it.renderer.RendererProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown_it.renderer.RendererProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "markdown_it.token.Token", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.renderer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.renderer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.renderer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.renderer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.renderer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown_it.renderer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "escapeHtml": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.escapeHtml", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "unescapeAll": {".class": "SymbolTableNode", "cross_ref": "markdown_it.common.utils.unescapeAll", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\markdown_it\\renderer.py"}