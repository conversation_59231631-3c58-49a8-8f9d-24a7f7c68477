{"data_mtime": 1754379926, "dep_lines": [29, 30, 31, 32, 40, 41, 42, 43, 44, 45, 46, 47, 1, 2, 3, 4, 5, 6, 7, 8, 9, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23, 24, 27], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["rich.pretty", "rich._loop", "rich.columns", "rich.console", "rich.constrain", "rich.highlighter", "rich.panel", "rich.scope", "rich.style", "rich.syntax", "rich.text", "rich.theme", "inspect", "linecache", "os", "sys", "dataclasses", "itertools", "traceback", "types", "typing", "rich", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "rich.jupyter", "rich.segment", "typing_extensions"], "hash": "e70f15d88af2be16f018cd1a1de0f8dd1a83a688", "id": "rich.traceback", "ignore_all": true, "interface_hash": "5cbc2342633b487f79e785f68e0cf51b21e6da89", "mtime": 1754379571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Documents\\Arcweb\\backend\\.venv\\Lib\\site-packages\\rich\\traceback.py", "plugin_data": null, "size": 35789, "suppressed": ["pygments.lexers", "pygments.token", "pygments.util"], "version_id": "1.15.0"}